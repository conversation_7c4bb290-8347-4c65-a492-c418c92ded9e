import 'package:flutter/foundation.dart';

import '../core/models/crossword_models.dart';
import '../core/services/storage_service.dart';
import '../core/services/sync_service.dart';

class UserStatsProvider extends ChangeNotifier {
  final StorageService _storageService = StorageService();
  final SyncService _syncService = SyncService();

  Map<String, dynamic> _statistics = {};
  bool _isInitialized = false;
  bool _isLoading = false;

  // Getters
  Map<String, dynamic> get statistics => Map.unmodifiable(_statistics);
  bool get isInitialized => _isInitialized;
  bool get isLoading => _isLoading;

  // Specific stat getters
  int get totalGamesPlayed => _statistics['totalGamesPlayed'] ?? 0;
  int get totalGamesCompleted => _statistics['totalGamesCompleted'] ?? 0;
  int get totalTimeSpent => _statistics['totalTimeSpent'] ?? 0; // in seconds
  int get averageCompletionTime => _statistics['averageCompletionTime'] ?? 0;
  int get bestCompletionTime => _statistics['bestCompletionTime'] ?? 0;
  int get currentStreak => _statistics['currentStreak'] ?? 0;
  int get longestStreak => _statistics['longestStreak'] ?? 0;
  int get totalCellsFilled => _statistics['totalCellsFilled'] ?? 0;
  int get totalHintsUsed => _statistics['totalHintsUsed'] ?? 0;
  String get favoriteCategory => _statistics['favoriteCategory'] ?? '';
  List<String> get achievements => List<String>.from(_statistics['achievements'] ?? []);
  String get lastPlayedDate => _statistics['lastPlayedDate'] ?? '';
  String get firstPlayedDate => _statistics['firstPlayedDate'] ?? '';

  // Calculated getters
  double get completionRate {
    if (totalGamesPlayed == 0) return 0.0;
    return (totalGamesCompleted / totalGamesPlayed) * 100;
  }

  String get averageCompletionTimeFormatted {
    if (averageCompletionTime == 0) return '0m 0s';
    final minutes = averageCompletionTime ~/ 60;
    final seconds = averageCompletionTime % 60;
    return '${minutes}m ${seconds}s';
  }

  String get bestCompletionTimeFormatted {
    if (bestCompletionTime == 0) return '0m 0s';
    final minutes = bestCompletionTime ~/ 60;
    final seconds = bestCompletionTime % 60;
    return '${minutes}m ${seconds}s';
  }

  String get totalTimeSpentFormatted {
    if (totalTimeSpent == 0) return '0h 0m';
    final hours = totalTimeSpent ~/ 3600;
    final minutes = (totalTimeSpent % 3600) ~/ 60;
    return '${hours}h ${minutes}m';
  }

  // Initialize the provider
  Future<void> initialize() async {
    if (_isInitialized) return;

    _isLoading = true;
    notifyListeners();

    try {
      _statistics = await _storageService.getUserStatistics();
      _isInitialized = true;
      debugPrint('UserStatsProvider initialized');
    } catch (e) {
      debugPrint('Failed to initialize UserStatsProvider: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Record game start
  Future<void> recordGameStart(String crosswordId, String? category) async {
    try {
      final updates = <String, dynamic>{
        'totalGamesPlayed': totalGamesPlayed + 1,
        'lastPlayedDate': DateTime.now().toIso8601String(),
      };

      // Set first played date if not set
      if (firstPlayedDate.isEmpty) {
        updates['firstPlayedDate'] = DateTime.now().toIso8601String();
      }

      // Update favorite category
      if (category != null && category.isNotEmpty) {
        updates['favoriteCategory'] = category;
      }

      await _updateStatistics(updates);
      await _checkAchievements();
    } catch (e) {
      debugPrint('Failed to record game start: $e');
    }
  }

  // Record game completion
  Future<void> recordGameCompletion(
    String crosswordId,
    Duration timeSpent,
    int cellsFilled,
    int hintsUsed,
    String? category,
  ) async {
    try {
      final completionTimeSeconds = timeSpent.inSeconds;
      final newTotalCompleted = totalGamesCompleted + 1;
      final newTotalTimeSpent = totalTimeSpent + completionTimeSeconds;
      final newAverageTime = newTotalTimeSpent ~/ newTotalCompleted;
      
      final updates = <String, dynamic>{
        'totalGamesCompleted': newTotalCompleted,
        'totalTimeSpent': newTotalTimeSpent,
        'averageCompletionTime': newAverageTime,
        'totalCellsFilled': totalCellsFilled + cellsFilled,
        'totalHintsUsed': totalHintsUsed + hintsUsed,
        'currentStreak': currentStreak + 1,
        'lastPlayedDate': DateTime.now().toIso8601String(),
      };

      // Update best completion time
      if (bestCompletionTime == 0 || completionTimeSeconds < bestCompletionTime) {
        updates['bestCompletionTime'] = completionTimeSeconds;
      }

      // Update longest streak
      if (currentStreak + 1 > longestStreak) {
        updates['longestStreak'] = currentStreak + 1;
      }

      // Update favorite category
      if (category != null && category.isNotEmpty) {
        updates['favoriteCategory'] = category;
      }

      await _updateStatistics(updates);
      await _checkAchievements();
    } catch (e) {
      debugPrint('Failed to record game completion: $e');
    }
  }

  // Record game abandonment (reset streak)
  Future<void> recordGameAbandonment() async {
    try {
      await _updateStatistics({'currentStreak': 0});
    } catch (e) {
      debugPrint('Failed to record game abandonment: $e');
    }
  }

  // Update statistics
  Future<void> _updateStatistics(Map<String, dynamic> updates) async {
    try {
      _statistics = {..._statistics, ...updates};
      await _storageService.updateUserStatistics(updates);
      
      // Add to sync queue for server sync
      await _syncService.addToSyncQueue(
        'user_stats',
        'user_statistics',
        _statistics,
      );
      
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to update statistics: $e');
    }
  }

  // Check and award achievements
  Future<void> _checkAchievements() async {
    final newAchievements = <String>[];
    final currentAchievements = achievements;

    // First game achievement
    if (totalGamesPlayed >= 1 && !currentAchievements.contains('first_game')) {
      newAchievements.add('first_game');
    }

    // First completion achievement
    if (totalGamesCompleted >= 1 && !currentAchievements.contains('first_completion')) {
      newAchievements.add('first_completion');
    }

    // Milestone achievements
    final milestones = [5, 10, 25, 50, 100];
    for (final milestone in milestones) {
      final achievementId = 'completed_$milestone';
      if (totalGamesCompleted >= milestone && !currentAchievements.contains(achievementId)) {
        newAchievements.add(achievementId);
      }
    }

    // Streak achievements
    final streakMilestones = [3, 5, 10, 20];
    for (final milestone in streakMilestones) {
      final achievementId = 'streak_$milestone';
      if (currentStreak >= milestone && !currentAchievements.contains(achievementId)) {
        newAchievements.add(achievementId);
      }
    }

    // Speed achievements (under 5 minutes, under 3 minutes, under 1 minute)
    if (bestCompletionTime > 0) {
      if (bestCompletionTime <= 60 && !currentAchievements.contains('speed_demon')) {
        newAchievements.add('speed_demon'); // Under 1 minute
      } else if (bestCompletionTime <= 180 && !currentAchievements.contains('speed_runner')) {
        newAchievements.add('speed_runner'); // Under 3 minutes
      } else if (bestCompletionTime <= 300 && !currentAchievements.contains('quick_solver')) {
        newAchievements.add('quick_solver'); // Under 5 minutes
      }
    }

    // Time spent achievements (1 hour, 5 hours, 10 hours, 24 hours)
    final timeSpentHours = totalTimeSpent / 3600;
    if (timeSpentHours >= 24 && !currentAchievements.contains('dedicated_player')) {
      newAchievements.add('dedicated_player');
    } else if (timeSpentHours >= 10 && !currentAchievements.contains('enthusiast')) {
      newAchievements.add('enthusiast');
    } else if (timeSpentHours >= 5 && !currentAchievements.contains('committed')) {
      newAchievements.add('committed');
    } else if (timeSpentHours >= 1 && !currentAchievements.contains('getting_started')) {
      newAchievements.add('getting_started');
    }

    // Award new achievements
    if (newAchievements.isNotEmpty) {
      final updatedAchievements = [...currentAchievements, ...newAchievements];
      await _updateStatistics({'achievements': updatedAchievements});
      debugPrint('New achievements awarded: $newAchievements');
    }
  }

  // Get achievement info
  Map<String, dynamic> getAchievementInfo(String achievementId) {
    final achievementData = {
      'first_game': {
        'title': 'Pemula',
        'description': 'Memulai permainan pertama',
        'icon': '🎮',
      },
      'first_completion': {
        'title': 'Penyelesai',
        'description': 'Menyelesaikan teka-teki pertama',
        'icon': '🏆',
      },
      'completed_5': {
        'title': 'Pemain Aktif',
        'description': 'Menyelesaikan 5 teka-teki',
        'icon': '⭐',
      },
      'completed_10': {
        'title': 'Penggemar',
        'description': 'Menyelesaikan 10 teka-teki',
        'icon': '🌟',
      },
      'completed_25': {
        'title': 'Ahli',
        'description': 'Menyelesaikan 25 teka-teki',
        'icon': '💫',
      },
      'completed_50': {
        'title': 'Master',
        'description': 'Menyelesaikan 50 teka-teki',
        'icon': '👑',
      },
      'completed_100': {
        'title': 'Legenda',
        'description': 'Menyelesaikan 100 teka-teki',
        'icon': '🏅',
      },
      'streak_3': {
        'title': 'Konsisten',
        'description': 'Menyelesaikan 3 teka-teki berturut-turut',
        'icon': '🔥',
      },
      'streak_5': {
        'title': 'Rajin',
        'description': 'Menyelesaikan 5 teka-teki berturut-turut',
        'icon': '🔥🔥',
      },
      'streak_10': {
        'title': 'Tak Terhentikan',
        'description': 'Menyelesaikan 10 teka-teki berturut-turut',
        'icon': '🔥🔥🔥',
      },
      'quick_solver': {
        'title': 'Pemecah Cepat',
        'description': 'Menyelesaikan dalam waktu kurang dari 5 menit',
        'icon': '⚡',
      },
      'speed_runner': {
        'title': 'Pelari Cepat',
        'description': 'Menyelesaikan dalam waktu kurang dari 3 menit',
        'icon': '💨',
      },
      'speed_demon': {
        'title': 'Iblis Kecepatan',
        'description': 'Menyelesaikan dalam waktu kurang dari 1 menit',
        'icon': '🚀',
      },
      'getting_started': {
        'title': 'Memulai',
        'description': 'Bermain selama 1 jam total',
        'icon': '⏰',
      },
      'committed': {
        'title': 'Berkomitmen',
        'description': 'Bermain selama 5 jam total',
        'icon': '⏳',
      },
      'enthusiast': {
        'title': 'Antusias',
        'description': 'Bermain selama 10 jam total',
        'icon': '⌛',
      },
      'dedicated_player': {
        'title': 'Pemain Berdedikasi',
        'description': 'Bermain selama 24 jam total',
        'icon': '🕰️',
      },
    };

    return achievementData[achievementId] ?? {
      'title': 'Unknown Achievement',
      'description': 'Achievement not found',
      'icon': '❓',
    };
  }

  // Reset all statistics
  Future<void> resetStatistics() async {
    try {
      await _storageService.clearUserStatistics();
      _statistics = await _storageService.getUserStatistics();
      notifyListeners();
      debugPrint('User statistics reset');
    } catch (e) {
      debugPrint('Failed to reset statistics: $e');
    }
  }

  // Export statistics
  Map<String, dynamic> exportStatistics() {
    return Map<String, dynamic>.from(_statistics);
  }
}
