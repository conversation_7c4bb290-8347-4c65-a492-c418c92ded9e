import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/models/crossword_models.dart';
import '../../providers/crossword_provider.dart';
import '../../providers/offline_provider.dart';
import '../../widgets/common/debug_info_widget.dart';
import '../../widgets/common/connectivity_status_widget.dart';
import '../../core/router/app_router.dart';

// State class for Selector to prevent unnecessary rebuilds
class CrosswordListState {
  final bool isLoading;
  final String? error;
  final List<Crossword> crosswords;
  final bool isLoadingMore;

  const CrosswordListState({
    required this.isLoading,
    required this.error,
    required this.crosswords,
    required this.isLoadingMore,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CrosswordListState &&
        other.isLoading == isLoading &&
        other.error == error &&
        other.crosswords.length == crosswords.length &&
        other.isLoadingMore == isLoadingMore;
  }

  @override
  int get hashCode {
    return isLoading.hashCode ^
        error.hashCode ^
        crosswords.length.hashCode ^
        isLoadingMore.hashCode;
  }
}

class CrosswordsListScreen extends StatefulWidget {
  final String? categoryId;
  final String? categoryName;

  const CrosswordsListScreen({
    super.key,
    this.categoryId,
    this.categoryName,
  });

  @override
  State<CrosswordsListScreen> createState() => _CrosswordsListScreenState();
}

class _CrosswordsListScreenState extends State<CrosswordsListScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  String _selectedDifficulty = 'all';
  String _sortBy = 'newest';
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadCrosswords();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadCrosswords({bool refresh = false}) async {
    try {
      final crosswordProvider = context.read<CrosswordProvider>();
      await crosswordProvider.loadCrosswords(
        category: widget.categoryId,
        difficulty: _selectedDifficulty == 'all' ? null : _selectedDifficulty,
        search: _searchQuery.isEmpty ? null : _searchQuery,
        refresh: refresh,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: ${e.toString()}')),
        );
      }
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreCrosswords();
    }
  }

  Future<void> _loadMoreCrosswords() async {
    final crosswordProvider = context.read<CrosswordProvider>();
    if (crosswordProvider.isLoadingMore || !crosswordProvider.hasMoreData) {
      return;
    }

    await crosswordProvider.loadMoreCrosswords();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });

    // Debounce search
    Future.delayed(const Duration(milliseconds: 500), () {
      if (_searchQuery == query) {
        _loadCrosswords(refresh: true);
      }
    });
  }

  void _onFilterChanged() {
    _loadCrosswords(refresh: true);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.categoryName ?? 'Daftar Teka-Teki'),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // Connectivity Banner
          const ConnectivityBanner(),

          // Search Bar
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              border: Border(
                bottom: BorderSide(
                  color: theme.colorScheme.outline.withValues(alpha: 0.3),
                ),
              ),
            ),
            child: TextField(
              controller: _searchController,
              onChanged: _onSearchChanged,
              decoration: InputDecoration(
                hintText: 'Cari teka-teki silang...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _onSearchChanged('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: theme.colorScheme.surfaceContainerHighest,
              ),
            ),
          ),

          // Filter Chips
          Container(
            height: 60,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        _buildFilterChip('Semua', 'all', _selectedDifficulty),
                        const SizedBox(width: 8),
                        _buildFilterChip('Mudah', 'mudah', _selectedDifficulty),
                        const SizedBox(width: 8),
                        _buildFilterChip(
                            'Sedang', 'sedang', _selectedDifficulty),
                        const SizedBox(width: 8),
                        _buildFilterChip('Sulit', 'sulit', _selectedDifficulty),
                      ],
                    ),
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    setState(() {
                      _sortBy = value;
                    });
                    _onFilterChanged();
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'newest',
                      child: Text('Terbaru'),
                    ),
                    const PopupMenuItem(
                      value: 'oldest',
                      child: Text('Terlama'),
                    ),
                    const PopupMenuItem(
                      value: 'popular',
                      child: Text('Terpopuler'),
                    ),
                    const PopupMenuItem(
                      value: 'rating',
                      child: Text('Rating Tertinggi'),
                    ),
                  ],
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: theme.colorScheme.outline.withValues(alpha: 0.3),
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.sort,
                          size: 16,
                          color: theme.colorScheme.onSurface,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _getSortLabel(_sortBy),
                          style: theme.textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Crosswords List
          Expanded(
            child: Selector<CrosswordProvider, CrosswordListState>(
              selector: (context, provider) => CrosswordListState(
                isLoading: provider.isLoading,
                error: provider.error,
                crosswords: provider.crosswords,
                isLoadingMore: provider.isLoadingMore,
              ),
              builder: (context, state, child) {
                if (state.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (state.error != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text('Error: ${state.error}'),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () => _loadCrosswords(refresh: true),
                          child: const Text('Coba Lagi'),
                        ),
                        const SizedBox(height: 24),
                        // Debug info untuk troubleshooting
                        const DebugInfoWidget(),
                      ],
                    ),
                  );
                }

                if (state.crosswords.isEmpty) {
                  return _buildEmptyState();
                }

                return RefreshIndicator(
                  onRefresh: () => _loadCrosswords(refresh: true),
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    itemCount:
                        state.crosswords.length + (state.isLoadingMore ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index == state.crosswords.length) {
                        return const Center(
                          child: Padding(
                            padding: EdgeInsets.all(16),
                            child: CircularProgressIndicator(),
                          ),
                        );
                      }
                      return _buildCrosswordCard(state.crosswords[index]);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value, String selectedValue) {
    final theme = Theme.of(context);
    final isSelected = selectedValue == value;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedDifficulty = value;
        });
        _onFilterChanged();
      },
      backgroundColor: theme.colorScheme.surface,
      selectedColor: theme.colorScheme.primary.withValues(alpha: 0.2),
      checkmarkColor: theme.colorScheme.primary,
      labelStyle: TextStyle(
        color: isSelected
            ? theme.colorScheme.primary
            : theme.colorScheme.onSurface,
      ),
    );
  }

  String _getSortLabel(String sortBy) {
    switch (sortBy) {
      case 'newest':
        return 'Terbaru';
      case 'oldest':
        return 'Terlama';
      case 'popular':
        return 'Populer';
      case 'rating':
        return 'Rating';
      default:
        return 'Urutkan';
    }
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'Tidak ada teka-teki silang ditemukan',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Coba ubah filter atau kata kunci pencarian',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              _searchController.clear();
              setState(() {
                _searchQuery = '';
                _selectedDifficulty = 'all';
                _sortBy = 'newest';
              });
              _loadCrosswords(refresh: true);
            },
            child: const Text('Reset Filter'),
          ),
        ],
      ),
    );
  }

  Widget _buildCrosswordCard(Crossword crossword) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToCrosswordDetail(crossword),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Preview Grid
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: Icon(Icons.grid_on, size: 32),
                ),
              ),

              const SizedBox(width: 16),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      crossword.title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 4),

                    if (crossword.description != null &&
                        crossword.description!.isNotEmpty)
                      Text(
                        crossword.description!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.7),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                    const SizedBox(height: 8),

                    // Metadata
                    Wrap(
                      spacing: 8,
                      runSpacing: 4,
                      children: [
                        _buildMetadataChip(
                          Icons.signal_cellular_alt,
                          _getDifficultyLabel(crossword.difficulty),
                          _getDifficultyColor(crossword.difficulty, theme),
                        ),
                        _buildMetadataChip(
                          Icons.play_arrow,
                          '${crossword.plays} dimainkan',
                          theme.colorScheme.primary,
                        ),
                        if (crossword.rating != null && crossword.rating! > 0)
                          _buildMetadataChip(
                            Icons.star,
                            crossword.rating!.toStringAsFixed(1),
                            Colors.amber,
                          ),
                      ],
                    ),
                  ],
                ),
              ),

              // Download/Offline Status
              Consumer<OfflineProvider>(
                builder: (context, offlineProvider, child) {
                  return _buildDownloadButton(crossword, offlineProvider);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMetadataChip(IconData icon, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 11,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  String _getDifficultyLabel(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'mudah':
        return 'Mudah';
      case 'sedang':
        return 'Sedang';
      case 'sulit':
        return 'Sulit';
      default:
        return difficulty;
    }
  }

  Color _getDifficultyColor(String difficulty, ThemeData theme) {
    switch (difficulty.toLowerCase()) {
      case 'mudah':
        return Colors.green;
      case 'sedang':
        return Colors.orange;
      case 'sulit':
        return Colors.red;
      default:
        return theme.colorScheme.primary;
    }
  }

  void _navigateToCrosswordDetail(Crossword crossword) {
    AppRouter.pushCrosswordDetail(context, crossword.id);
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter & Urutkan'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Tingkat Kesulitan:'),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: [
                _buildFilterChip('Semua', 'all', _selectedDifficulty),
                _buildFilterChip('Mudah', 'mudah', _selectedDifficulty),
                _buildFilterChip('Sedang', 'sedang', _selectedDifficulty),
                _buildFilterChip('Sulit', 'sulit', _selectedDifficulty),
              ],
            ),
            const SizedBox(height: 16),
            const Text('Urutkan berdasarkan:'),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _sortBy,
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _sortBy = value;
                  });
                }
              },
              items: const [
                DropdownMenuItem(value: 'newest', child: Text('Terbaru')),
                DropdownMenuItem(value: 'oldest', child: Text('Terlama')),
                DropdownMenuItem(value: 'popular', child: Text('Terpopuler')),
                DropdownMenuItem(
                    value: 'rating', child: Text('Rating Tertinggi')),
              ],
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                isDense: true,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Batal'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _onFilterChanged();
            },
            child: const Text('Terapkan'),
          ),
        ],
      ),
    );
  }

  Widget _buildDownloadButton(
      Crossword crossword, OfflineProvider offlineProvider) {
    final isDownloaded = offlineProvider.isCrosswordDownloaded(crossword.id);
    final downloadStatus = offlineProvider.getDownloadStatus(crossword.id);
    final downloadProgress = offlineProvider.getDownloadProgress(crossword.id);

    if (downloadStatus == DownloadStatus.downloading) {
      return Container(
        width: 40,
        height: 40,
        padding: const EdgeInsets.all(8),
        child: CircularProgressIndicator(
          value: downloadProgress,
          strokeWidth: 2,
        ),
      );
    }

    if (isDownloaded) {
      return IconButton(
        icon: const Icon(Icons.download_done, color: Colors.green),
        onPressed: () => _showDownloadOptions(crossword, offlineProvider),
        tooltip: 'Tersimpan offline',
      );
    }

    if (!offlineProvider.isOnline) {
      return const Icon(
        Icons.cloud_off,
        color: Colors.grey,
      );
    }

    return IconButton(
      icon: const Icon(Icons.download),
      onPressed: () => _downloadCrossword(crossword, offlineProvider),
      tooltip: 'Download untuk offline',
    );
  }

  void _downloadCrossword(
      Crossword crossword, OfflineProvider offlineProvider) async {
    final success = await offlineProvider.downloadCrossword(crossword.id);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success
                ? 'Teka-teki berhasil didownload'
                : 'Gagal mendownload teka-teki',
          ),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  void _showDownloadOptions(
      Crossword crossword, OfflineProvider offlineProvider) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.play_arrow),
              title: const Text('Mainkan'),
              onTap: () {
                Navigator.pop(context);
                _navigateToCrosswordDetail(crossword);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete),
              title: const Text('Hapus dari offline'),
              onTap: () async {
                Navigator.pop(context);
                final success = await offlineProvider
                    .removeDownloadedCrossword(crossword.id);

                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        success
                            ? 'Teka-teki dihapus dari penyimpanan offline'
                            : 'Gagal menghapus teka-teki',
                      ),
                      backgroundColor: success ? Colors.green : Colors.red,
                    ),
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
