import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/auth_provider.dart';
import '../../providers/user_stats_provider.dart';
import '../../providers/offline_provider.dart';
import '../../widgets/common/connectivity_status_widget.dart';
import '../../core/router/app_router.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Initialize providers
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<UserStatsProvider>().initialize();
      context.read<OfflineProvider>().initialize();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Profil'),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => AppRouter.pushSettings(context),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Profil', icon: Icon(Icons.person)),
            Tab(text: 'Statistik', icon: Icon(Icons.analytics)),
            Tab(text: 'Pencapaian', icon: Icon(Icons.emoji_events)),
          ],
        ),
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          if (!authProvider.isAuthenticated) {
            return _buildNotLoggedInView(context, theme);
          }

          return Column(
            children: [
              // Connectivity Status Banner
              const ConnectivityBanner(),

              // Tab Content
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildProfileTab(context, theme, authProvider),
                    _buildStatisticsTab(context, theme),
                    _buildAchievementsTab(context, theme),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildNotLoggedInView(BuildContext context, ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.person_outline,
              size: 80,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 24),
            Text(
              'Belum Masuk',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Masuk untuk melihat profil dan menyimpan progress permainan',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => AppRouter.goToLogin(context),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Masuk'),
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: () => AppRouter.goToRegister(context),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Daftar'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Profile Tab
  Widget _buildProfileTab(
      BuildContext context, ThemeData theme, AuthProvider authProvider) {
    final user = authProvider.user!;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Profile Header
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // Avatar
                  CircleAvatar(
                    radius: 50,
                    backgroundColor:
                        theme.colorScheme.primary.withValues(alpha: 0.1),
                    backgroundImage: user.avatarUrl != null
                        ? NetworkImage(user.avatarUrl!)
                        : null,
                    child: user.avatarUrl == null
                        ? Icon(
                            Icons.person,
                            size: 50,
                            color: theme.colorScheme.primary,
                          )
                        : null,
                  ),

                  const SizedBox(height: 16),

                  // Display Name
                  Text(
                    authProvider.displayName,
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 4),

                  // Username
                  Text(
                    '@${user.username}',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Email
                  Text(
                    user.email,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),

                  // Bio if available
                  if (user.bio != null && user.bio!.isNotEmpty) ...[
                    const SizedBox(height: 12),
                    Text(
                      user.bio!,
                      style: theme.textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Stats Cards
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  context,
                  'Bergabung',
                  _formatDate(user.createdAt),
                  Icons.calendar_today,
                  theme,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Status',
                  'Aktif',
                  Icons.badge,
                  theme,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Connectivity Status
          const ConnectivityStatusWidget(),

          const SizedBox(height: 16),

          // Menu Items
          _buildMenuItem(
            context,
            'Edit Profil',
            'Ubah informasi profil Anda',
            Icons.edit,
            () => AppRouter.pushEditProfile(context),
            theme,
          ),

          const SizedBox(height: 8),

          _buildMenuItem(
            context,
            'Progress Permainan',
            'Lihat riwayat dan progress permainan',
            Icons.timeline,
            () => AppRouter.goToProgress(context),
            theme,
          ),

          const SizedBox(height: 8),

          Consumer<OfflineProvider>(
            builder: (context, offlineProvider, child) {
              return _buildMenuItem(
                context,
                'Download Offline',
                '${offlineProvider.downloadedCrosswordIds.length} teka-teki tersimpan',
                Icons.download,
                () => _showOfflineManagementDialog(context),
                theme,
              );
            },
          ),

          const SizedBox(height: 8),

          _buildMenuItem(
            context,
            'Pengaturan',
            'Atur preferensi aplikasi',
            Icons.settings,
            () {
              Navigator.of(context).pushNamed('/settings');
            },
            theme,
          ),

          const SizedBox(height: 24),

          // Logout Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _showLogoutDialog(context, authProvider),
              icon: const Icon(Icons.logout),
              label: const Text('Keluar'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                foregroundColor: theme.colorScheme.error,
                side: BorderSide(color: theme.colorScheme.error),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    ThemeData theme,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              icon,
              color: theme.colorScheme.primary,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
    ThemeData theme,
  ) {
    return Card(
      child: ListTile(
        leading: Icon(icon, color: theme.colorScheme.primary),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.chevron_right),
        onTap: onTap,
      ),
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'Mei',
      'Jun',
      'Jul',
      'Agu',
      'Sep',
      'Okt',
      'Nov',
      'Des'
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  // Statistics Tab
  Widget _buildStatisticsTab(BuildContext context, ThemeData theme) {
    return Consumer<UserStatsProvider>(
      builder: (context, statsProvider, child) {
        if (statsProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Overview Stats
              _buildStatsOverview(theme, statsProvider),

              const SizedBox(height: 24),

              // Detailed Stats
              _buildDetailedStats(theme, statsProvider),
            ],
          ),
        );
      },
    );
  }

  // Achievements Tab
  Widget _buildAchievementsTab(BuildContext context, ThemeData theme) {
    return Consumer<UserStatsProvider>(
      builder: (context, statsProvider, child) {
        if (statsProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final achievements = statsProvider.achievements;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Pencapaian (${achievements.length})',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              if (achievements.isEmpty)
                _buildEmptyAchievements(theme)
              else
                _buildAchievementsList(theme, statsProvider, achievements),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatsOverview(ThemeData theme, UserStatsProvider statsProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Ringkasan Statistik',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Dimainkan',
                    statsProvider.totalGamesPlayed.toString(),
                    Icons.play_circle,
                    theme,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Diselesaikan',
                    statsProvider.totalGamesCompleted.toString(),
                    Icons.check_circle,
                    theme,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Tingkat Selesai',
                    '${statsProvider.completionRate.toStringAsFixed(1)}%',
                    Icons.trending_up,
                    theme,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Streak Saat Ini',
                    statsProvider.currentStreak.toString(),
                    Icons.local_fire_department,
                    theme,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedStats(ThemeData theme, UserStatsProvider statsProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Statistik Detail',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildDetailStatRow('Total Waktu Bermain',
                statsProvider.totalTimeSpentFormatted, Icons.timer, theme),
            _buildDetailStatRow(
                'Waktu Rata-rata',
                statsProvider.averageCompletionTimeFormatted,
                Icons.access_time,
                theme),
            _buildDetailStatRow('Waktu Terbaik',
                statsProvider.bestCompletionTimeFormatted, Icons.speed, theme),
            _buildDetailStatRow(
                'Streak Terpanjang',
                '${statsProvider.longestStreak} permainan',
                Icons.whatshot,
                theme),
            _buildDetailStatRow('Total Sel Diisi',
                '${statsProvider.totalCellsFilled} sel', Icons.grid_on, theme),
            _buildDetailStatRow(
                'Total Petunjuk',
                '${statsProvider.totalHintsUsed} petunjuk',
                Icons.lightbulb,
                theme),
            if (statsProvider.favoriteCategory.isNotEmpty)
              _buildDetailStatRow('Kategori Favorit',
                  statsProvider.favoriteCategory, Icons.favorite, theme),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailStatRow(
      String label, String value, IconData icon, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: theme.colorScheme.primary),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: theme.textTheme.bodyMedium,
            ),
          ),
          Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyAchievements(ThemeData theme) {
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.emoji_events,
            size: 64,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'Belum Ada Pencapaian',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Mulai bermain untuk mendapatkan pencapaian pertama Anda!',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAchievementsList(ThemeData theme,
      UserStatsProvider statsProvider, List<String> achievements) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: achievements.length,
      itemBuilder: (context, index) {
        final achievementId = achievements[index];
        final achievementInfo = statsProvider.getAchievementInfo(achievementId);

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: theme.colorScheme.primary.withValues(alpha: 0.1),
              child: Text(
                achievementInfo['icon'],
                style: const TextStyle(fontSize: 20),
              ),
            ),
            title: Text(
              achievementInfo['title'],
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Text(achievementInfo['description']),
            trailing: const Icon(
              Icons.check_circle,
              color: Colors.green,
            ),
          ),
        );
      },
    );
  }

  void _showOfflineManagementDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Manajemen Offline'),
        content: Consumer<OfflineProvider>(
          builder: (context, offlineProvider, child) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                    'Teka-teki tersimpan: ${offlineProvider.downloadedCrosswordIds.length}'),
                const SizedBox(height: 8),
                Text(
                    'Status sinkronisasi: ${offlineProvider.hasPendingSync ? "Menunggu" : "Tersinkronisasi"}'),
                if (offlineProvider.hasPendingSync)
                  Text('Item pending: ${offlineProvider.pendingSyncCount}'),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Tutup'),
          ),
          Consumer<OfflineProvider>(
            builder: (context, offlineProvider, child) {
              return TextButton(
                onPressed: offlineProvider.isOnline
                    ? () {
                        Navigator.of(context).pop();
                        offlineProvider.forcSync();
                      }
                    : null,
                child: const Text('Sinkronkan'),
              );
            },
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, AuthProvider authProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Keluar'),
        content: const Text('Yakin ingin keluar dari akun Anda?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Batal'),
          ),
          ElevatedButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              navigator.pop();
              await authProvider.logout();
              if (context.mounted) {
                AppRouter.replaceWithHome(context);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Keluar'),
          ),
        ],
      ),
    );
  }
}
