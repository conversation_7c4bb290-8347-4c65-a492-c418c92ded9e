import 'package:flutter/foundation.dart';

import '../core/models/crossword_models.dart';
import '../core/services/api_service.dart';
import '../core/services/storage_service.dart';
import '../core/services/connectivity_service.dart';
import '../core/services/sync_service.dart';

enum DownloadStatus {
  idle,
  downloading,
  completed,
  error,
}

class OfflineProvider extends ChangeNotifier {
  final ApiService _apiService;
  final StorageService _storageService = StorageService();
  final ConnectivityService _connectivityService = ConnectivityService();
  final SyncService _syncService = SyncService();

  OfflineProvider(this._apiService);

  // State variables
  List<String> _downloadedCrosswordIds = [];
  Map<String, DownloadStatus> _downloadStatuses = {};
  Map<String, double> _downloadProgress = {};
  bool _isInitialized = false;

  // Getters
  List<String> get downloadedCrosswordIds => List.unmodifiable(_downloadedCrosswordIds);
  Map<String, DownloadStatus> get downloadStatuses => Map.unmodifiable(_downloadStatuses);
  Map<String, double> get downloadProgress => Map.unmodifiable(_downloadProgress);
  bool get isInitialized => _isInitialized;
  bool get isOnline => _connectivityService.isOnline;
  bool get hasPendingSync => _syncService.hasPendingItems;
  int get pendingSyncCount => _syncService.pendingItemsCount;

  // Initialize the provider
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadDownloadedCrosswords();
      _connectivityService.addListener(_onConnectivityChanged);
      _syncService.addListener(_onSyncStatusChanged);
      
      _isInitialized = true;
      debugPrint('OfflineProvider initialized with ${_downloadedCrosswordIds.length} downloaded crosswords');
    } catch (e) {
      debugPrint('Failed to initialize OfflineProvider: $e');
    }
    
    notifyListeners();
  }

  // Load downloaded crosswords from storage
  Future<void> _loadDownloadedCrosswords() async {
    try {
      _downloadedCrosswordIds = await _storageService.getOfflineCrosswordIds();
      
      // Initialize download statuses
      for (final id in _downloadedCrosswordIds) {
        _downloadStatuses[id] = DownloadStatus.completed;
        _downloadProgress[id] = 1.0;
      }
    } catch (e) {
      debugPrint('Failed to load downloaded crosswords: $e');
      _downloadedCrosswordIds = [];
    }
  }

  // Download crossword for offline use
  Future<bool> downloadCrossword(String crosswordId) async {
    if (!_connectivityService.isOnline) {
      debugPrint('Cannot download crossword: offline');
      return false;
    }

    if (_downloadedCrosswordIds.contains(crosswordId)) {
      debugPrint('Crossword already downloaded: $crosswordId');
      return true;
    }

    try {
      _downloadStatuses[crosswordId] = DownloadStatus.downloading;
      _downloadProgress[crosswordId] = 0.0;
      notifyListeners();

      // Simulate download progress
      for (int i = 1; i <= 10; i++) {
        await Future.delayed(const Duration(milliseconds: 100));
        _downloadProgress[crosswordId] = i / 10.0;
        notifyListeners();
      }

      // Fetch crossword data from API
      final response = await _apiService.getCrossword(crosswordId);
      
      if (response.isSuccess && response.data != null) {
        final crossword = response.data!;
        
        // Save to offline storage
        await _storageService.saveOfflineCrossword(crosswordId, crossword);
        
        // Update state
        _downloadedCrosswordIds.add(crosswordId);
        _downloadStatuses[crosswordId] = DownloadStatus.completed;
        _downloadProgress[crosswordId] = 1.0;
        
        debugPrint('Successfully downloaded crossword: $crosswordId');
        notifyListeners();
        return true;
      } else {
        throw Exception('Failed to fetch crossword: ${response.message}');
      }
    } catch (e) {
      debugPrint('Failed to download crossword $crosswordId: $e');
      _downloadStatuses[crosswordId] = DownloadStatus.error;
      _downloadProgress[crosswordId] = 0.0;
      notifyListeners();
      return false;
    }
  }

  // Remove downloaded crossword
  Future<bool> removeDownloadedCrossword(String crosswordId) async {
    try {
      await _storageService.removeOfflineCrossword(crosswordId);
      
      _downloadedCrosswordIds.remove(crosswordId);
      _downloadStatuses.remove(crosswordId);
      _downloadProgress.remove(crosswordId);
      
      debugPrint('Removed downloaded crossword: $crosswordId');
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Failed to remove downloaded crossword $crosswordId: $e');
      return false;
    }
  }

  // Get offline crossword
  Future<Crossword?> getOfflineCrossword(String crosswordId) async {
    if (!_downloadedCrosswordIds.contains(crosswordId)) {
      return null;
    }

    try {
      return await _storageService.getOfflineCrossword(crosswordId);
    } catch (e) {
      debugPrint('Failed to get offline crossword $crosswordId: $e');
      return null;
    }
  }

  // Check if crossword is downloaded
  bool isCrosswordDownloaded(String crosswordId) {
    return _downloadedCrosswordIds.contains(crosswordId);
  }

  // Get download status for a crossword
  DownloadStatus getDownloadStatus(String crosswordId) {
    return _downloadStatuses[crosswordId] ?? DownloadStatus.idle;
  }

  // Get download progress for a crossword
  double getDownloadProgress(String crosswordId) {
    return _downloadProgress[crosswordId] ?? 0.0;
  }

  // Clear all downloaded crosswords
  Future<void> clearAllDownloads() async {
    try {
      await _storageService.clearAllOfflineCrosswords();
      
      _downloadedCrosswordIds.clear();
      _downloadStatuses.clear();
      _downloadProgress.clear();
      
      debugPrint('Cleared all downloaded crosswords');
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to clear all downloads: $e');
    }
  }

  // Download multiple crosswords
  Future<void> downloadMultipleCrosswords(List<String> crosswordIds) async {
    if (!_connectivityService.isOnline) {
      debugPrint('Cannot download crosswords: offline');
      return;
    }

    for (final id in crosswordIds) {
      if (!_downloadedCrosswordIds.contains(id)) {
        await downloadCrossword(id);
        // Small delay between downloads to prevent overwhelming the server
        await Future.delayed(const Duration(milliseconds: 500));
      }
    }
  }

  // Force sync with server
  Future<void> forcSync() async {
    if (_connectivityService.isOnline) {
      await _syncService.forcSync();
    }
  }

  // Handle connectivity changes
  void _onConnectivityChanged() {
    notifyListeners();
    
    if (_connectivityService.isOnline) {
      debugPrint('Connectivity restored - triggering sync');
      _syncService.forcSync();
    }
  }

  // Handle sync status changes
  void _onSyncStatusChanged() {
    notifyListeners();
  }

  // Get storage usage info
  Future<Map<String, dynamic>> getStorageInfo() async {
    try {
      final downloadedCount = _downloadedCrosswordIds.length;
      final progressKeys = await _storageService.getAllProgressKeys();
      
      return {
        'downloadedCrosswords': downloadedCount,
        'savedProgress': progressKeys.length,
        'pendingSync': _syncService.pendingItemsCount,
        'isOnline': _connectivityService.isOnline,
        'lastSync': _syncService.lastSyncTime?.toIso8601String(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'downloadedCrosswords': 0,
        'savedProgress': 0,
        'pendingSync': 0,
        'isOnline': false,
      };
    }
  }

  @override
  void dispose() {
    _connectivityService.removeListener(_onConnectivityChanged);
    _syncService.removeListener(_onSyncStatusChanged);
    super.dispose();
  }
}
