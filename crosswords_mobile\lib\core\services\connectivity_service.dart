import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';

enum ConnectivityStatus {
  online,
  offline,
  unknown,
}

class ConnectivityService extends ChangeNotifier {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  ConnectivityStatus _status = ConnectivityStatus.unknown;
  bool _isInitialized = false;
  
  // Getters
  ConnectivityStatus get status => _status;
  bool get isOnline => _status == ConnectivityStatus.online;
  bool get isOffline => _status == ConnectivityStatus.offline;
  bool get isInitialized => _isInitialized;

  // Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Check initial connectivity
      final result = await _connectivity.checkConnectivity();
      _updateStatus(result);

      // Listen for connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        _updateStatus,
        onError: (error) {
          debugPrint('Connectivity error: $error');
          _status = ConnectivityStatus.unknown;
          notifyListeners();
        },
      );

      _isInitialized = true;
      debugPrint('ConnectivityService initialized with status: $_status');
    } catch (e) {
      debugPrint('Failed to initialize ConnectivityService: $e');
      _status = ConnectivityStatus.unknown;
    }
    
    notifyListeners();
  }

  // Update connectivity status
  void _updateStatus(List<ConnectivityResult> results) {
    final ConnectivityStatus newStatus;
    
    if (results.contains(ConnectivityResult.mobile) ||
        results.contains(ConnectivityResult.wifi) ||
        results.contains(ConnectivityResult.ethernet)) {
      newStatus = ConnectivityStatus.online;
    } else if (results.contains(ConnectivityResult.none)) {
      newStatus = ConnectivityStatus.offline;
    } else {
      newStatus = ConnectivityStatus.unknown;
    }

    if (_status != newStatus) {
      final previousStatus = _status;
      _status = newStatus;
      
      debugPrint('Connectivity changed: $previousStatus -> $newStatus');
      
      // Notify listeners about the change
      notifyListeners();
      
      // Trigger sync if we just came online
      if (previousStatus == ConnectivityStatus.offline && 
          newStatus == ConnectivityStatus.online) {
        _onConnectivityRestored();
      }
    }
  }

  // Called when connectivity is restored
  void _onConnectivityRestored() {
    debugPrint('Connectivity restored - triggering sync');
    // This will be used by sync service to trigger background sync
  }

  // Check if we have a specific type of connection
  bool hasWifiConnection() {
    return _connectivity.checkConnectivity().then((results) {
      return results.contains(ConnectivityResult.wifi);
    }) as bool;
  }

  bool hasMobileConnection() {
    return _connectivity.checkConnectivity().then((results) {
      return results.contains(ConnectivityResult.mobile);
    }) as bool;
  }

  // Force refresh connectivity status
  Future<void> refresh() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateStatus(result);
    } catch (e) {
      debugPrint('Failed to refresh connectivity: $e');
    }
  }

  // Dispose resources
  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  // Get connectivity type as string for UI display
  String getConnectionTypeString() {
    switch (_status) {
      case ConnectivityStatus.online:
        return 'Online';
      case ConnectivityStatus.offline:
        return 'Offline';
      case ConnectivityStatus.unknown:
        return 'Unknown';
    }
  }

  // Get detailed connection info
  Future<Map<String, dynamic>> getConnectionInfo() async {
    try {
      final results = await _connectivity.checkConnectivity();
      return {
        'status': _status.toString(),
        'types': results.map((r) => r.toString()).toList(),
        'isOnline': isOnline,
        'isOffline': isOffline,
      };
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
        'isOnline': false,
        'isOffline': true,
      };
    }
  }
}
