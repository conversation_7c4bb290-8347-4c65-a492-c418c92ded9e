import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/services/connectivity_service.dart';
import '../../providers/offline_provider.dart';

class ConnectivityStatusWidget extends StatelessWidget {
  final bool showSyncStatus;
  final bool compact;

  const ConnectivityStatusWidget({
    super.key,
    this.showSyncStatus = true,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer2<ConnectivityService, OfflineProvider>(
      builder: (context, connectivityService, offlineProvider, child) {
        if (!connectivityService.isInitialized) {
          return const SizedBox.shrink();
        }

        return _buildStatusWidget(
          context,
          connectivityService,
          offlineProvider,
        );
      },
    );
  }

  Widget _buildStatusWidget(
    BuildContext context,
    ConnectivityService connectivityService,
    OfflineProvider offlineProvider,
  ) {
    final theme = Theme.of(context);
    final isOnline = connectivityService.isOnline;
    final hasPendingSync = offlineProvider.hasPendingSync;

    if (compact) {
      return _buildCompactStatus(theme, isOnline, hasPendingSync);
    }

    return _buildFullStatus(
      context,
      theme,
      connectivityService,
      offlineProvider,
    );
  }

  Widget _buildCompactStatus(
    ThemeData theme,
    bool isOnline,
    bool hasPendingSync,
  ) {
    final color = isOnline ? Colors.green : Colors.red;
    final icon = isOnline ? Icons.wifi : Icons.wifi_off;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          if (hasPendingSync && showSyncStatus) ...[
            const SizedBox(width: 4),
            Icon(
              Icons.sync,
              size: 14,
              color: Colors.orange,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFullStatus(
    BuildContext context,
    ThemeData theme,
    ConnectivityService connectivityService,
    OfflineProvider offlineProvider,
  ) {
    final isOnline = connectivityService.isOnline;
    final hasPendingSync = offlineProvider.hasPendingSync;
    final pendingSyncCount = offlineProvider.pendingSyncCount;

    return Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Connection Status
            Row(
              children: [
                Icon(
                  isOnline ? Icons.wifi : Icons.wifi_off,
                  color: isOnline ? Colors.green : Colors.red,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  isOnline ? 'Online' : 'Offline',
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: isOnline ? Colors.green : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (isOnline)
                  Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.green,
                      shape: BoxShape.circle,
                    ),
                  ),
              ],
            ),

            if (showSyncStatus) ...[
              const SizedBox(height: 8),
              const Divider(height: 1),
              const SizedBox(height: 8),

              // Sync Status
              Row(
                children: [
                  Icon(
                    hasPendingSync ? Icons.sync_problem : Icons.sync,
                    color: hasPendingSync ? Colors.orange : Colors.green,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      hasPendingSync
                          ? 'Menunggu sinkronisasi ($pendingSyncCount item)'
                          : 'Tersinkronisasi',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: hasPendingSync ? Colors.orange : Colors.green,
                      ),
                    ),
                  ),
                  if (hasPendingSync && isOnline)
                    TextButton(
                      onPressed: () => offlineProvider.forcSync(),
                      child: const Text('Sinkronkan'),
                    ),
                ],
              ),
            ],

            // Offline Downloads Info
            if (offlineProvider.downloadedCrosswordIds.isNotEmpty) ...[
              const SizedBox(height: 8),
              const Divider(height: 1),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(
                    Icons.download_done,
                    color: Colors.blue,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${offlineProvider.downloadedCrosswordIds.length} teka-teki offline',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class ConnectivityBanner extends StatelessWidget {
  const ConnectivityBanner({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ConnectivityService>(
      builder: (context, connectivityService, child) {
        if (connectivityService.isOnline || !connectivityService.isInitialized) {
          return const SizedBox.shrink();
        }

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          color: Colors.orange.withValues(alpha: 0.9),
          child: Row(
            children: [
              const Icon(
                Icons.wifi_off,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'Mode offline - Beberapa fitur mungkin terbatas',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              TextButton(
                onPressed: () => connectivityService.refresh(),
                child: const Text(
                  'Coba Lagi',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class SyncStatusIndicator extends StatelessWidget {
  const SyncStatusIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<OfflineProvider>(
      builder: (context, offlineProvider, child) {
        if (!offlineProvider.hasPendingSync) {
          return const SizedBox.shrink();
        }

        return Container(
          margin: const EdgeInsets.all(8),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Menyinkronkan ${offlineProvider.pendingSyncCount} item...',
                style: const TextStyle(
                  color: Colors.orange,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class OfflineIndicator extends StatelessWidget {
  const OfflineIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ConnectivityService>(
      builder: (context, connectivityService, child) {
        if (connectivityService.isOnline || !connectivityService.isInitialized) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
          ),
          child: const Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.wifi_off,
                size: 14,
                color: Colors.red,
              ),
              SizedBox(width: 4),
              Text(
                'Offline',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
