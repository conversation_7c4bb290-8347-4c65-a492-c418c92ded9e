import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../screens/home/<USER>';
import '../../screens/categories/categories_screen.dart';
import '../../screens/crosswords/crosswords_list_screen.dart';
import '../../screens/progress/progress_screen.dart';
import '../../providers/auth_provider.dart';

class MainNavigationWrapper extends StatefulWidget {
  final int initialIndex;

  const MainNavigationWrapper({
    super.key,
    this.initialIndex = 0,
  });

  @override
  State<MainNavigationWrapper> createState() => _MainNavigationWrapperState();
}

class _MainNavigationWrapperState extends State<MainNavigationWrapper> {
  late int _currentIndex;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authProvider = context.watch<AuthProvider>();

    return Scaffold(
      body: PageView(
        controller: _pageController,
        onPageChanged: _onPageChanged,
        children: const [
          HomeScreen(),
          CategoriesScreen(),
          CrosswordsListScreen(),
          ProgressScreen(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
        selectedItemColor: theme.colorScheme.primary,
        unselectedItemColor: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        backgroundColor: theme.colorScheme.surface,
        elevation: 8,
        items: [
          const BottomNavigationBarItem(
            icon: Icon(Icons.home),
            activeIcon: Icon(Icons.home),
            label: 'Beranda',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.category),
            activeIcon: Icon(Icons.category),
            label: 'Kategori',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.grid_3x3),
            activeIcon: Icon(Icons.grid_3x3),
            label: 'Teka-Teki',
          ),
          BottomNavigationBarItem(
            icon: authProvider.isAuthenticated 
                ? const Icon(Icons.timeline)
                : const Icon(Icons.login),
            activeIcon: authProvider.isAuthenticated 
                ? const Icon(Icons.timeline)
                : const Icon(Icons.login),
            label: authProvider.isAuthenticated ? 'Progress' : 'Masuk',
          ),
        ],
      ),
    );
  }
}
