import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../../core/models/crossword_models.dart';
import '../../providers/crossword_provider.dart';

class CategoryDetailScreen extends StatefulWidget {
  final String categorySlug;

  const CategoryDetailScreen({
    super.key,
    required this.categorySlug,
  });

  @override
  State<CategoryDetailScreen> createState() => _CategoryDetailScreenState();
}

class _CategoryDetailScreenState extends State<CategoryDetailScreen> {
  bool _isLoading = true;
  CrosswordCategory? _category;
  List<Crossword> _crosswords = [];
  String? _error;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadCategoryData();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreCrosswords();
    }
  }

  Future<void> _loadCategoryData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final crosswordProvider = context.read<CrosswordProvider>();

      // Load categories to find the current one
      await crosswordProvider.loadCategories();
      final categories = crosswordProvider.categories;

      // Find category by slug
      _category = categories.firstWhere(
        (cat) => cat.slug == widget.categorySlug,
        orElse: () => throw Exception('Category not found'),
      );

      // Load crosswords for this category
      await crosswordProvider.loadCrosswords(
        category: _category!.id,
        refresh: true,
      );

      _crosswords = crosswordProvider.crosswords;

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadMoreCrosswords() async {
    if (_category == null) return;

    final crosswordProvider = context.read<CrosswordProvider>();
    if (!crosswordProvider.isLoadingMore && crosswordProvider.hasMoreData) {
      await crosswordProvider.loadMoreCrosswords();

      if (mounted) {
        setState(() {
          _crosswords = crosswordProvider.crosswords;
        });
      }
    }
  }

  Future<void> _onRefresh() async {
    await _loadCategoryData();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_error != null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Error'),
          automaticallyImplyLeading: true,
        ),
        body: _buildErrorView(theme),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_category?.name ?? 'Detail Kategori'),
        automaticallyImplyLeading: true,
        elevation: 0,
      ),
      body: _isLoading ? _buildLoadingView() : _buildContent(theme),
    );
  }

  Widget _buildLoadingView() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildErrorView(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: theme.colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Gagal memuat kategori',
            style: theme.textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadCategoryData,
            child: const Text('Coba Lagi'),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(ThemeData theme) {
    if (_crosswords.isEmpty) {
      return _buildEmptyView(theme);
    }

    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: Column(
        children: [
          // Category Header
          if (_category != null) _buildCategoryHeader(theme),

          // Crosswords List
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(16),
              itemCount: _crosswords.length +
                  (context.watch<CrosswordProvider>().isLoadingMore ? 1 : 0),
              itemBuilder: (context, index) {
                if (index >= _crosswords.length) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                final crossword = _crosswords[index];
                return _buildCrosswordCard(crossword, theme);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyView(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.quiz_outlined,
            size: 64,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'Belum ada teka-teki silang',
            style: theme.textTheme.titleLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Teka-teki silang untuk kategori ini akan muncul di sini',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryHeader(ThemeData theme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color:
                      _getCategoryColor(_category!.name).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _getCategoryIcon(_category!.name),
                  size: 24,
                  color: _getCategoryColor(_category!.name),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _category!.name,
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${_category!.crosswordCount} teka-teki tersedia',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color:
                            theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (_category!.description != null) ...[
            const SizedBox(height: 12),
            Text(
              _category!.description!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCrosswordCard(Crossword crossword, ThemeData theme) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _navigateToCrossword(crossword),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      crossword.title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getDifficultyColor(crossword.difficulty)
                          .withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      crossword.difficulty.toUpperCase(),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: _getDifficultyColor(crossword.difficulty),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              if (crossword.description != null) ...[
                const SizedBox(height: 8),
                Text(
                  crossword.description!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.grid_3x3,
                    size: 16,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${crossword.gridSize}x${crossword.gridSize}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.play_circle_outline,
                    size: 16,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${crossword.plays} dimainkan',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                  if (crossword.rating != null) ...[
                    const SizedBox(width: 16),
                    const Icon(
                      Icons.star,
                      size: 16,
                      color: Colors.amber,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      crossword.rating!.toStringAsFixed(1),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color:
                            theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper methods
  IconData _getCategoryIcon(String categoryName) {
    switch (categoryName.toLowerCase()) {
      case 'umum':
        return Icons.quiz;
      case 'sejarah':
        return Icons.history_edu;
      case 'geografi':
        return Icons.public;
      case 'sains':
        return Icons.science;
      case 'olahraga':
        return Icons.sports_soccer;
      case 'hiburan':
        return Icons.movie;
      case 'teknologi':
        return Icons.computer;
      case 'makanan':
        return Icons.restaurant;
      case 'hewan':
        return Icons.pets;
      case 'tumbuhan':
        return Icons.local_florist;
      default:
        return Icons.category;
    }
  }

  Color _getCategoryColor(String categoryName) {
    switch (categoryName.toLowerCase()) {
      case 'umum':
        return Colors.blue;
      case 'sejarah':
        return Colors.brown;
      case 'geografi':
        return Colors.green;
      case 'sains':
        return Colors.purple;
      case 'olahraga':
        return Colors.orange;
      case 'hiburan':
        return Colors.pink;
      case 'teknologi':
        return Colors.indigo;
      case 'makanan':
        return Colors.red;
      case 'hewan':
        return Colors.amber;
      case 'tumbuhan':
        return Colors.lightGreen;
      default:
        return Colors.grey;
    }
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'mudah':
        return Colors.green;
      case 'sedang':
        return Colors.orange;
      case 'sulit':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  void _navigateToCrossword(Crossword crossword) {
    context.push('/teka-teki-silang/${crossword.id}');
  }
}
