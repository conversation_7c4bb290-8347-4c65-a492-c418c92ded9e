import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/offline_provider.dart';
import '../../core/services/connectivity_service.dart';
import '../../widgets/common/connectivity_status_widget.dart';

class OfflineManagementScreen extends StatefulWidget {
  const OfflineManagementScreen({super.key});

  @override
  State<OfflineManagementScreen> createState() => _OfflineManagementScreenState();
}

class _OfflineManagementScreenState extends State<OfflineManagementScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<OfflineProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Manajemen Offline'),
        elevation: 0,
        actions: [
          Consumer<OfflineProvider>(
            builder: (context, offlineProvider, child) {
              return IconButton(
                icon: const Icon(Icons.sync),
                onPressed: offlineProvider.isOnline
                    ? () => offlineProvider.forcSync()
                    : null,
                tooltip: 'Sinkronkan',
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Connectivity Banner
          const ConnectivityBanner(),
          
          // Status Overview
          Consumer2<OfflineProvider, ConnectivityService>(
            builder: (context, offlineProvider, connectivityService, child) {
              return _buildStatusOverview(theme, offlineProvider, connectivityService);
            },
          ),
          
          const SizedBox(height: 16),
          
          // Downloaded Crosswords List
          Expanded(
            child: Consumer<OfflineProvider>(
              builder: (context, offlineProvider, child) {
                return _buildDownloadedCrosswordsList(theme, offlineProvider);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusOverview(
    ThemeData theme,
    OfflineProvider offlineProvider,
    ConnectivityService connectivityService,
  ) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Status Offline',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Connection Status
            Row(
              children: [
                Icon(
                  connectivityService.isOnline ? Icons.wifi : Icons.wifi_off,
                  color: connectivityService.isOnline ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  connectivityService.isOnline ? 'Online' : 'Offline',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: connectivityService.isOnline ? Colors.green : Colors.red,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Downloaded Count
            Row(
              children: [
                const Icon(Icons.download_done, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  '${offlineProvider.downloadedCrosswordIds.length} teka-teki tersimpan',
                  style: theme.textTheme.bodyLarge,
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Sync Status
            if (offlineProvider.hasPendingSync) ...[
              Row(
                children: [
                  const Icon(Icons.sync_problem, color: Colors.orange),
                  const SizedBox(width: 8),
                  Text(
                    '${offlineProvider.pendingSyncCount} item menunggu sinkronisasi',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: Colors.orange,
                    ),
                  ),
                ],
              ),
            ] else ...[
              const Row(
                children: [
                  Icon(Icons.sync, color: Colors.green),
                  SizedBox(width: 8),
                  Text(
                    'Semua data tersinkronisasi',
                    style: TextStyle(color: Colors.green),
                  ),
                ],
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: connectivityService.isOnline
                        ? () => offlineProvider.forcSync()
                        : null,
                    icon: const Icon(Icons.sync),
                    label: const Text('Sinkronkan'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showClearAllDialog(),
                    icon: const Icon(Icons.delete_sweep),
                    label: const Text('Hapus Semua'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDownloadedCrosswordsList(ThemeData theme, OfflineProvider offlineProvider) {
    final downloadedIds = offlineProvider.downloadedCrosswordIds;
    
    if (downloadedIds.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.cloud_download,
              size: 64,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'Belum Ada Teka-teki Offline',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Download teka-teki untuk dimainkan secara offline',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'Teka-teki Offline (${downloadedIds.length})',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        
        const SizedBox(height: 8),
        
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: downloadedIds.length,
            itemBuilder: (context, index) {
              final crosswordId = downloadedIds[index];
              return _buildOfflineCrosswordCard(theme, crosswordId, offlineProvider);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildOfflineCrosswordCard(
    ThemeData theme,
    String crosswordId,
    OfflineProvider offlineProvider,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.grid_on,
            color: theme.colorScheme.primary,
          ),
        ),
        title: Text('Teka-teki $crosswordId'),
        subtitle: const Text('Tersimpan offline'),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleCrosswordAction(value, crosswordId, offlineProvider),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'play',
              child: ListTile(
                leading: Icon(Icons.play_arrow),
                title: Text('Mainkan'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('Hapus'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleCrosswordAction(
    String action,
    String crosswordId,
    OfflineProvider offlineProvider,
  ) async {
    switch (action) {
      case 'play':
        // Navigate to crossword game
        // AppRouter.pushCrosswordGame(context, crosswordId);
        break;
      case 'delete':
        final success = await offlineProvider.removeDownloadedCrossword(crosswordId);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                success
                    ? 'Teka-teki dihapus dari penyimpanan offline'
                    : 'Gagal menghapus teka-teki',
              ),
              backgroundColor: success ? Colors.green : Colors.red,
            ),
          );
        }
        break;
    }
  }

  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Hapus Semua Teka-teki Offline'),
        content: const Text(
          'Yakin ingin menghapus semua teka-teki yang tersimpan offline? '
          'Tindakan ini tidak dapat dibatalkan.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Batal'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final offlineProvider = context.read<OfflineProvider>();
              await offlineProvider.clearAllDownloads();
              
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Semua teka-teki offline telah dihapus'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Hapus Semua'),
          ),
        ],
      ),
    );
  }
}
