class AppConfig {
  // API Configuration
  // Ganti dengan IP address komputer Anda atau domain server
  static const String baseUrl =
      'https://tekatekiio.widiyanata.com/api'; // Ganti dengan IP address yang sesuai
  static const String apiKey =
      '45da56e9bb395c346441bc8034a919809f4411dc4eb3884f7ce99a05cf6d9dd5';

  // App Information
  static const String appName = 'tekateki.io';
  static const String appVersion = '1.0.0';

  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String themeKey = 'theme_mode';
  static const String progressKey = 'crossword_progress';
  static const String offlineCrosswordKey = 'offline_crossword';
  static const String userStatsKey = 'user_statistics';
  static const String syncQueueKey = 'sync_queue';
  static const String connectivityKey = 'connectivity_status';

  // Game Configuration
  static const int maxGridSize = 30;
  static const int minGridSize = 5;
  static const int defaultGridSize = 15;

  // UI Configuration
  static const double defaultCellSize = 40.0;
  static const double minCellSize = 30.0;
  static const double maxCellSize = 60.0;

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);

  // Timeouts
  static const Duration apiTimeout = Duration(seconds: 30);
  static const Duration connectionTimeout = Duration(seconds: 10);

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 50;

  // Rating Configuration
  static const int minRating = 1;
  static const int maxRating = 5;

  // Indonesian Language Support
  static const String defaultLocale = 'id_ID';
  static const String fallbackLocale = 'en_US';

  // URL Patterns
  static const String crosswordUrlPattern = 'teka-teki-silang';
  static const String categoryUrlPattern = 'kategori';

  // Cache Configuration
  static const Duration cacheExpiry = Duration(hours: 24);
  static const int maxCacheSize = 100; // Number of items

  // Development flags
  static const bool isDebugMode = true;
  static const bool enableLogging = true;
  static const bool enableAnalytics = false;
}
