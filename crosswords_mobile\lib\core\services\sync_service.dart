import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/crossword_models.dart';
import 'api_service.dart';
import 'storage_service.dart';
import 'connectivity_service.dart';

enum SyncStatus {
  idle,
  syncing,
  success,
  error,
}

class SyncItem {
  final String id;
  final String type; // 'progress', 'user_data', etc.
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final int retryCount;

  const SyncItem({
    required this.id,
    required this.type,
    required this.data,
    required this.timestamp,
    this.retryCount = 0,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type,
    'data': data,
    'timestamp': timestamp.toIso8601String(),
    'retryCount': retryCount,
  };

  factory SyncItem.fromJson(Map<String, dynamic> json) => SyncItem(
    id: json['id'],
    type: json['type'],
    data: Map<String, dynamic>.from(json['data']),
    timestamp: DateTime.parse(json['timestamp']),
    retryCount: json['retryCount'] ?? 0,
  );

  SyncItem copyWith({int? retryCount}) => SyncItem(
    id: id,
    type: type,
    data: data,
    timestamp: timestamp,
    retryCount: retryCount ?? this.retryCount,
  );
}

class SyncService extends ChangeNotifier {
  static final SyncService _instance = SyncService._internal();
  factory SyncService() => _instance;
  SyncService._internal();

  final ApiService _apiService = ApiService(''); // Will be injected
  final StorageService _storageService = StorageService();
  final ConnectivityService _connectivityService = ConnectivityService();
  
  static const String _syncQueueKey = 'sync_queue';
  static const String _lastSyncKey = 'last_sync_timestamp';
  static const int _maxRetries = 3;
  
  SyncStatus _status = SyncStatus.idle;
  List<SyncItem> _syncQueue = [];
  DateTime? _lastSyncTime;
  Timer? _syncTimer;
  bool _isInitialized = false;

  // Getters
  SyncStatus get status => _status;
  List<SyncItem> get syncQueue => List.unmodifiable(_syncQueue);
  DateTime? get lastSyncTime => _lastSyncTime;
  bool get hasPendingItems => _syncQueue.isNotEmpty;
  int get pendingItemsCount => _syncQueue.length;

  // Initialize the sync service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadSyncQueue();
      await _loadLastSyncTime();
      
      // Listen for connectivity changes
      _connectivityService.addListener(_onConnectivityChanged);
      
      // Start periodic sync timer (every 5 minutes when online)
      _startSyncTimer();
      
      _isInitialized = true;
      debugPrint('SyncService initialized with ${_syncQueue.length} pending items');
      
      // Try initial sync if online
      if (_connectivityService.isOnline) {
        _performSync();
      }
    } catch (e) {
      debugPrint('Failed to initialize SyncService: $e');
    }
  }

  // Add item to sync queue
  Future<void> addToSyncQueue(String type, String id, Map<String, dynamic> data) async {
    final syncItem = SyncItem(
      id: id,
      type: type,
      data: data,
      timestamp: DateTime.now(),
    );

    _syncQueue.add(syncItem);
    await _saveSyncQueue();
    
    debugPrint('Added to sync queue: $type - $id');
    notifyListeners();

    // Try immediate sync if online
    if (_connectivityService.isOnline && _status == SyncStatus.idle) {
      _performSync();
    }
  }

  // Remove item from sync queue
  Future<void> _removeFromSyncQueue(String id) async {
    _syncQueue.removeWhere((item) => item.id == id);
    await _saveSyncQueue();
    notifyListeners();
  }

  // Perform sync operation
  Future<void> _performSync() async {
    if (_status == SyncStatus.syncing || !_connectivityService.isOnline) {
      return;
    }

    if (_syncQueue.isEmpty) {
      _status = SyncStatus.idle;
      notifyListeners();
      return;
    }

    _status = SyncStatus.syncing;
    notifyListeners();

    try {
      final itemsToSync = List<SyncItem>.from(_syncQueue);
      
      for (final item in itemsToSync) {
        try {
          await _syncItem(item);
          await _removeFromSyncQueue(item.id);
        } catch (e) {
          debugPrint('Failed to sync item ${item.id}: $e');
          
          // Increment retry count
          final updatedItem = item.copyWith(retryCount: item.retryCount + 1);
          final index = _syncQueue.indexWhere((i) => i.id == item.id);
          
          if (index != -1) {
            if (updatedItem.retryCount >= _maxRetries) {
              // Remove item after max retries
              await _removeFromSyncQueue(item.id);
              debugPrint('Removed item ${item.id} after max retries');
            } else {
              // Update retry count
              _syncQueue[index] = updatedItem;
              await _saveSyncQueue();
            }
          }
        }
      }

      _lastSyncTime = DateTime.now();
      await _saveLastSyncTime();
      
      _status = _syncQueue.isEmpty ? SyncStatus.success : SyncStatus.error;
    } catch (e) {
      debugPrint('Sync operation failed: $e');
      _status = SyncStatus.error;
    }

    notifyListeners();
  }

  // Sync individual item
  Future<void> _syncItem(SyncItem item) async {
    switch (item.type) {
      case 'progress':
        await _syncProgress(item);
        break;
      case 'user_data':
        await _syncUserData(item);
        break;
      default:
        debugPrint('Unknown sync item type: ${item.type}');
    }
  }

  // Sync progress data
  Future<void> _syncProgress(SyncItem item) async {
    final crosswordId = item.data['crosswordId'] as String;
    final progressData = item.data['progress'] as Map<String, dynamic>;
    
    final response = await _apiService.saveProgress(crosswordId, progressData);
    
    if (!response.isSuccess) {
      throw Exception('Failed to sync progress: ${response.message}');
    }
  }

  // Sync user data
  Future<void> _syncUserData(SyncItem item) async {
    final userData = item.data;
    final response = await _apiService.updateProfile(userData);
    
    if (!response.isSuccess) {
      throw Exception('Failed to sync user data: ${response.message}');
    }
  }

  // Handle connectivity changes
  void _onConnectivityChanged() {
    if (_connectivityService.isOnline && _syncQueue.isNotEmpty) {
      debugPrint('Connectivity restored - starting sync');
      _performSync();
    }
  }

  // Start periodic sync timer
  void _startSyncTimer() {
    _syncTimer?.cancel();
    _syncTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      if (_connectivityService.isOnline && _syncQueue.isNotEmpty) {
        _performSync();
      }
    });
  }

  // Manual sync trigger
  Future<void> forcSync() async {
    if (_connectivityService.isOnline) {
      await _performSync();
    }
  }

  // Load sync queue from storage
  Future<void> _loadSyncQueue() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queueJson = prefs.getString(_syncQueueKey);
      
      if (queueJson != null) {
        final queueData = json.decode(queueJson) as List;
        _syncQueue = queueData.map((item) => SyncItem.fromJson(item)).toList();
      }
    } catch (e) {
      debugPrint('Failed to load sync queue: $e');
      _syncQueue = [];
    }
  }

  // Save sync queue to storage
  Future<void> _saveSyncQueue() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queueJson = json.encode(_syncQueue.map((item) => item.toJson()).toList());
      await prefs.setString(_syncQueueKey, queueJson);
    } catch (e) {
      debugPrint('Failed to save sync queue: $e');
    }
  }

  // Load last sync time
  Future<void> _loadLastSyncTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getString(_lastSyncKey);
      if (timestamp != null) {
        _lastSyncTime = DateTime.parse(timestamp);
      }
    } catch (e) {
      debugPrint('Failed to load last sync time: $e');
    }
  }

  // Save last sync time
  Future<void> _saveLastSyncTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_lastSyncTime != null) {
        await prefs.setString(_lastSyncKey, _lastSyncTime!.toIso8601String());
      }
    } catch (e) {
      debugPrint('Failed to save last sync time: $e');
    }
  }

  // Clear sync queue
  Future<void> clearSyncQueue() async {
    _syncQueue.clear();
    await _saveSyncQueue();
    notifyListeners();
  }

  @override
  void dispose() {
    _syncTimer?.cancel();
    _connectivityService.removeListener(_onConnectivityChanged);
    super.dispose();
  }
}
