import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import '../core/models/crossword_models.dart';
import '../core/services/storage_service.dart';
import '../core/services/sync_service.dart';
import 'user_stats_provider.dart';

enum GameState { idle, playing, paused, completed }

enum Direction { across, down }

class SelectedCell {
  final int row;
  final int col;
  final Direction direction;
  final int wordNumber;
  final String clue;

  const SelectedCell({
    required this.row,
    required this.col,
    required this.direction,
    required this.wordNumber,
    required this.clue,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SelectedCell &&
          runtimeType == other.runtimeType &&
          row == other.row &&
          col == other.col &&
          direction == other.direction;

  @override
  int get hashCode => row.hashCode ^ col.hashCode ^ direction.hashCode;
}

class CrosswordGameProvider extends ChangeNotifier {
  final StorageService _storageService = StorageService();
  final SyncService _syncService = SyncService();
  UserStatsProvider? _userStatsProvider;

  // Game data
  Crossword? _crossword;
  List<List<String>> _userInput = [];
  GameState _gameState = GameState.idle;

  // Selection state
  SelectedCell? _selectedCell;
  List<SelectedCell> _highlightedCells = [];

  // Game progress
  Duration _timeSpent = Duration.zero;
  DateTime? _gameStartTime;
  int _completedCells = 0;
  int _totalCells = 0;
  int _hintsUsed = 0;

  // Settings
  bool _autoMoveEnabled = true;
  bool _showErrors = true;
  bool _vibrationEnabled = true;

  // Performance optimization: cache word information
  final Map<String, Map<String, dynamic>?> _wordInfoCache = {};
  Map<String, dynamic>? _currentWordInfo;

  // Set user stats provider for tracking
  void setUserStatsProvider(UserStatsProvider? provider) {
    _userStatsProvider = provider;
  }

  // Performance monitoring
  int _wordFindingCalls = 0;
  int _cacheHits = 0;

  // Helper method for conditional debug logging
  void _debugLog(String message) {
    if (kDebugMode) {
      debugPrint(message);
    }
  }

  // Performance monitoring getters
  double get cacheHitRatio =>
      _wordFindingCalls > 0 ? _cacheHits / _wordFindingCalls : 0.0;

  // Getters
  Crossword? get crossword => _crossword;
  List<List<String>> get userInput => _userInput;
  GameState get gameState => _gameState;
  SelectedCell? get selectedCell => _selectedCell;
  List<SelectedCell> get highlightedCells => _highlightedCells;
  Duration get timeSpent => _timeSpent;
  int get completedCells => _completedCells;
  int get totalCells => _totalCells;
  int get hintsUsed => _hintsUsed;
  double get progressPercentage =>
      _totalCells > 0 ? (_completedCells / _totalCells) * 100 : 0.0;
  bool get isCompleted => _gameState == GameState.completed;
  bool get autoMoveEnabled => _autoMoveEnabled;
  bool get showErrors => _showErrors;
  bool get vibrationEnabled => _vibrationEnabled;

  // Initialize game with crossword data
  Future<void> initializeGame(Crossword crossword,
      {bool resumeProgress = false}) async {
    _crossword = crossword;
    _gameState = GameState.idle;

    // Clear performance caches for new game
    _wordInfoCache.clear();
    _currentWordInfo = null;
    _wordFindingCalls = 0;
    _cacheHits = 0;

    // Reset timer and progress state for new games
    if (!resumeProgress) {
      _timeSpent = Duration.zero;
      _gameStartTime = null;
      _completedCells = 0;
      _hintsUsed = 0;
      _selectedCell = null;
      _highlightedCells.clear();
    }

    // Initialize user input grid
    _userInput = List.generate(
      crossword.gridSize,
      (row) => List.generate(crossword.gridSize, (col) => ''),
    );

    // Calculate total cells
    _totalCells = 0;
    for (int row = 0; row < crossword.gridSize; row++) {
      for (int col = 0; col < crossword.gridSize; col++) {
        if (!crossword.gridData[row][col].isEmpty &&
            !crossword.gridData[row][col].isBlack) {
          _totalCells++;
        }
      }
    }

    // Load settings
    await _loadSettings();

    // Resume progress if requested
    if (resumeProgress) {
      await _loadProgress();
    } else {
      // Ensure completed cells is recalculated for fresh games
      _updateCompletedCells();
    }

    // Debug: Log crossword structure
    _debugLogCrosswordStructure();

    notifyListeners();
  }

  // Debug method to log crossword structure
  void _debugLogCrosswordStructure() {
    if (_crossword == null) {
      debugPrint('DEBUG: Crossword is null');
      return;
    }

    debugPrint('DEBUG: Crossword Structure:');
    debugPrint('  - ID: ${_crossword!.id}');
    debugPrint('  - Title: ${_crossword!.title}');
    debugPrint('  - Grid Size: ${_crossword!.gridSize}');
    debugPrint('  - Word Positions Count: ${_crossword!.wordPositions.length}');
    debugPrint('  - Clues Across Count: ${_crossword!.clues.across.length}');
    debugPrint('  - Clues Down Count: ${_crossword!.clues.down.length}');

    // Log first few word positions for debugging
    for (int i = 0; i < _crossword!.wordPositions.length && i < 5; i++) {
      final wp = _crossword!.wordPositions[i];
      debugPrint(
          '  - WordPosition $i: "${wp.word}" at (${wp.row}, ${wp.col}) ${wp.direction} #${wp.number}');
    }

    // Log grid sample (first few rows)
    debugPrint('  - Grid Sample (first 5 rows):');
    for (int row = 0; row < _crossword!.gridSize && row < 5; row++) {
      String rowStr = '    Row $row: ';
      for (int col = 0; col < _crossword!.gridSize && col < 10; col++) {
        final cell = _crossword!.gridData[row][col];
        rowStr += '[${cell.char.isEmpty ? ' ' : cell.char}]';
      }
      debugPrint(rowStr);
    }

    // Log specific cells mentioned in the test case
    debugPrint('  - Test Case Cells:');
    _debugLogCell(0, 5, 'JOKER start');
    _debugLogCell(4, 5, 'Intersection R');
    _debugLogCell(4, 4, 'FROZEN start');
    _debugLogCell(2, 5, 'JOKER middle K');
    _debugLogCell(4, 7, 'FROZEN middle Z');
  }

  // Debug helper to log specific cell information
  void _debugLogCell(int row, int col, String description) {
    if (_crossword == null ||
        row >= _crossword!.gridSize ||
        col >= _crossword!.gridSize ||
        row < 0 ||
        col < 0) {
      debugPrint('    $description ($row, $col): OUT OF BOUNDS');
      return;
    }

    final cell = _crossword!.gridData[row][col];
    debugPrint(
        '    $description ($row, $col): char="${cell.char}", wordIds=${cell.wordIds}, isEmpty=${cell.isEmpty}, isBlack=${cell.isBlack}');
  }

  // Start the game
  void startGame() {
    if (_gameState == GameState.idle || _gameState == GameState.paused) {
      _gameState = GameState.playing;
      _gameStartTime = DateTime.now();

      // Track game start in statistics (only for new games, not resumed)
      if (_gameState == GameState.idle && _crossword != null) {
        _userStatsProvider?.recordGameStart(
          _crossword!.id,
          _crossword!.categoryName,
        );
      }

      notifyListeners();
    }
  }

  // Pause the game
  void pauseGame() {
    if (_gameState == GameState.playing) {
      _gameState = GameState.paused;
      _updateTimeSpent();
      notifyListeners();
    }
  }

  // Resume the game
  void resumeGame() {
    if (_gameState == GameState.paused) {
      _gameState = GameState.playing;
      _gameStartTime = DateTime.now();
      notifyListeners();
    }
  }

  // Select a cell
  void selectCell(int row, int col, {Direction? preferredDirection}) {
    debugPrint('GameProvider selectCell called: ($row, $col)');

    if (_crossword == null) {
      debugPrint('selectCell: crossword is null');
      return;
    }

    // Start game if not started
    if (_gameState == GameState.idle) {
      debugPrint('selectCell: starting game from idle');
      startGame();
    }

    final cell = _crossword!.gridData[row][col];
    debugPrint(
        'selectCell: cell isEmpty=${cell.isEmpty}, isBlack=${cell.isBlack}');

    // Only allow selection of playable cells (not empty and not black)
    if (cell.isEmpty || cell.isBlack) {
      debugPrint('selectCell: cell not selectable');
      return;
    }

    // Determine direction and word
    Direction direction = preferredDirection ?? Direction.across;
    debugPrint('selectCell: trying direction $direction first');

    // Find the word that contains this cell
    Map<String, dynamic>? wordInfo = _findWordAtPosition(row, col, direction);
    debugPrint('selectCell: wordInfo for $direction: $wordInfo');

    if (wordInfo == null) {
      // Try the other direction
      direction =
          direction == Direction.across ? Direction.down : Direction.across;
      debugPrint('selectCell: trying other direction $direction');
      wordInfo = _findWordAtPosition(row, col, direction);
      debugPrint('selectCell: wordInfo for $direction: $wordInfo');

      if (wordInfo == null) {
        debugPrint('selectCell: no word found in either direction');
        debugPrint('selectCell: creating fallback selection for isolated cell');

        // Create a fallback selection for cells that don't belong to any word
        // This allows selection of individual cells even if they're not part of a word
        final selectedCell = SelectedCell(
          row: row,
          col: col,
          direction: Direction.across, // Default direction
          wordNumber: 0, // No word number
          clue: '', // No clue
        );

        _selectedCell = selectedCell;
        _highlightedCells.clear(); // No word to highlight

        // Provide haptic feedback
        if (_vibrationEnabled) {
          HapticFeedback.selectionClick();
        }

        debugPrint(
            'selectCell: fallback completed. Selected cell: $_selectedCell');
        notifyListeners();
        return;
      }
    }

    // Check if same cell is selected to toggle direction
    if (_selectedCell?.row == row && _selectedCell?.col == col) {
      debugPrint(
          'selectCell: same cell selected, attempting to toggle direction');
      final currentDirection = _selectedCell!.direction;
      final newDirection = currentDirection == Direction.across
          ? Direction.down
          : Direction.across;

      debugPrint(
          'selectCell: trying to toggle from $currentDirection to $newDirection');
      final newWordInfo = _findWordAtPosition(row, col, newDirection);

      if (newWordInfo != null) {
        debugPrint('selectCell: found word in new direction, toggling');
        _selectedCell = SelectedCell(
          row: row,
          col: col,
          direction: newDirection,
          wordNumber: newWordInfo['number'],
          clue: newWordInfo['clue'],
        );
        _currentWordInfo = newWordInfo;
      } else {
        debugPrint(
            'selectCell: no word found in new direction, keeping current selection');
        // Keep current selection, just update highlighting
      }
    } else {
      // New cell selection
      debugPrint('selectCell: new cell selection');
      _selectedCell = SelectedCell(
        row: row,
        col: col,
        direction: direction,
        wordNumber: wordInfo['number'],
        clue: wordInfo['clue'],
      );
      _currentWordInfo = wordInfo;
    }

    // Update highlighted cells
    _updateHighlightedCells();

    // Provide haptic feedback
    if (_vibrationEnabled) {
      HapticFeedback.selectionClick();
    }

    debugPrint('selectCell: completed. Selected cell: $_selectedCell');
    debugPrint(
        'selectCell: highlighted cells count: ${_highlightedCells.length}');

    notifyListeners();
  }

  // Input character at selected cell
  void inputCharacter(String character) {
    if (_selectedCell == null ||
        _crossword == null ||
        _gameState != GameState.playing) {
      return;
    }

    final row = _selectedCell!.row;
    final col = _selectedCell!.col;

    // Validate input (only letters)
    if (character.isNotEmpty && !RegExp(r'^[a-zA-Z]$').hasMatch(character)) {
      return;
    }

    // Update user input
    _userInput[row][col] = character.toUpperCase();

    // Update completed cells count
    _updateCompletedCells();

    // Check if game is completed
    if (_isGameCompleted()) {
      _completeGame();
      return;
    }

    // Auto-move to next cell if enabled
    if (_autoMoveEnabled && character.isNotEmpty) {
      _moveToNextCell();
    }

    // Provide haptic feedback
    if (_vibrationEnabled) {
      HapticFeedback.lightImpact();
    }

    notifyListeners();
  }

  // Delete character at selected cell
  void deleteCharacter() {
    if (_selectedCell == null || _gameState != GameState.playing) {
      return;
    }

    final row = _selectedCell!.row;
    final col = _selectedCell!.col;

    if (_userInput[row][col].isNotEmpty) {
      _userInput[row][col] = '';
      _updateCompletedCells();
    } else if (_autoMoveEnabled) {
      // Move to previous cell and delete
      _moveToPreviousCell();
      if (_selectedCell != null) {
        _userInput[_selectedCell!.row][_selectedCell!.col] = '';
        _updateCompletedCells();
      }
    }

    // Provide haptic feedback
    if (_vibrationEnabled) {
      HapticFeedback.lightImpact();
    }

    notifyListeners();
  }

  // Move to next cell in current word
  void _moveToNextCell() {
    if (_selectedCell == null || _crossword == null) return;

    final direction = _selectedCell!.direction;
    int nextRow = _selectedCell!.row;
    int nextCol = _selectedCell!.col;

    if (direction == Direction.across) {
      nextCol++;
    } else {
      nextRow++;
    }

    // Check if next cell is valid and part of the same word
    if (_isValidCell(nextRow, nextCol) &&
        _isPartOfCurrentWord(nextRow, nextCol)) {
      _selectCellOptimized(nextRow, nextCol, direction);
    }
  }

  // Move to previous cell in current word
  void _moveToPreviousCell() {
    if (_selectedCell == null || _crossword == null) return;

    final direction = _selectedCell!.direction;
    int prevRow = _selectedCell!.row;
    int prevCol = _selectedCell!.col;

    if (direction == Direction.across) {
      prevCol--;
    } else {
      prevRow--;
    }

    // Check if previous cell is valid and part of the same word
    if (_isValidCell(prevRow, prevCol) &&
        _isPartOfCurrentWord(prevRow, prevCol)) {
      _selectCellOptimized(prevRow, prevCol, direction);
    }
  }

  // Check if cell is part of current word (performance optimized)
  bool _isPartOfCurrentWord(int row, int col) {
    if (_currentWordInfo == null || _selectedCell == null) {
      return true; // Fallback to normal behavior
    }

    // Use cached word info to check if cell is within word bounds
    if (_currentWordInfo!.containsKey('startRow') &&
        _currentWordInfo!.containsKey('startCol') &&
        _currentWordInfo!.containsKey('word')) {
      final startRow = _currentWordInfo!['startRow'] as int;
      final startCol = _currentWordInfo!['startCol'] as int;
      final word = _currentWordInfo!['word'] as String;

      if (_selectedCell!.direction == Direction.across) {
        return row == startRow &&
            col >= startCol &&
            col < startCol + word.length;
      } else {
        return col == startCol &&
            row >= startRow &&
            row < startRow + word.length;
      }
    }

    return true; // Fallback to normal behavior
  }

  // Optimized cell selection for auto-move (avoids full word finding)
  void _selectCellOptimized(int row, int col, Direction direction) {
    if (_currentWordInfo == null) {
      // Fallback to normal selection
      selectCell(row, col, preferredDirection: direction);
      return;
    }

    // Create selected cell using cached word info
    final selectedCell = SelectedCell(
      row: row,
      col: col,
      direction: direction,
      wordNumber: _currentWordInfo!['number'] ?? 0,
      clue: _currentWordInfo!['clue'] ?? '',
    );

    _selectedCell = selectedCell;

    // Update highlighted cells - we need to recalculate to ensure highlighting is correct
    _updateHighlightedCells();

    // Provide haptic feedback
    if (_vibrationEnabled) {
      HapticFeedback.selectionClick();
    }

    debugPrint(
        '_selectCellOptimized: completed. Selected cell: $_selectedCell');
    debugPrint(
        '_selectCellOptimized: highlighted cells count: ${_highlightedCells.length}');

    notifyListeners();
  }

  // Check if cell coordinates are valid
  bool _isValidCell(int row, int col) {
    if (_crossword == null) return false;

    if (row < 0 ||
        row >= _crossword!.gridSize ||
        col < 0 ||
        col >= _crossword!.gridSize) {
      return false;
    }

    final cell = _crossword!.gridData[row][col];
    return !cell.isEmpty && !cell.isBlack;
  }

  // Find word information at position
  Map<String, dynamic>? _findWordAtPosition(
      int row, int col, Direction direction) {
    if (_crossword == null) {
      debugPrint('_findWordAtPosition: crossword is null');
      return null;
    }

    // Performance monitoring
    _wordFindingCalls++;

    // Performance optimization: check cache first
    final cacheKey = '${row}_${col}_${direction.toString()}';
    if (_wordInfoCache.containsKey(cacheKey)) {
      _cacheHits++;
      final cachedResult = _wordInfoCache[cacheKey];
      _debugLog(
          '_findWordAtPosition: returning cached result for $cacheKey (hit ratio: ${cacheHitRatio.toStringAsFixed(2)})');
      return cachedResult;
    }

    _debugLog(
        '_findWordAtPosition: searching for ($row, $col) in direction $direction');
    _debugLog(
        '_findWordAtPosition: total wordPositions: ${_crossword!.wordPositions.length}');

    Map<String, dynamic>? result;

    // Method 1: Use wordPositions array (preferred method)
    final wordFromPositions = _findWordFromPositions(row, col, direction);
    if (wordFromPositions != null) {
      debugPrint('_findWordAtPosition: found word using wordPositions');
      result = wordFromPositions;
    } else {
      // Method 2: Fallback - Use wordIds in grid cells
      debugPrint('_findWordAtPosition: trying fallback method using wordIds');
      final wordFromGrid = _findWordFromGridCells(row, col, direction);
      if (wordFromGrid != null) {
        debugPrint('_findWordAtPosition: found word using grid wordIds');
        result = wordFromGrid;
      }
    }

    // Cache the result (including null results to avoid repeated searches)
    _wordInfoCache[cacheKey] = result;

    if (result == null) {
      debugPrint('_findWordAtPosition: no word found with either method');
    }

    return result;
  }

  // Method 1: Find word using wordPositions array
  Map<String, dynamic>? _findWordFromPositions(
      int row, int col, Direction direction) {
    for (final wordPos in _crossword!.wordPositions) {
      debugPrint(
          '_findWordFromPositions: checking wordPos - row:${wordPos.row}, col:${wordPos.col}, word:"${wordPos.word}", isAcross:${wordPos.isAcross}, isDown:${wordPos.isDown}, number:${wordPos.number}');

      if (direction == Direction.across && wordPos.isAcross) {
        debugPrint(
            '_findWordFromPositions: checking across word at (${wordPos.row}, ${wordPos.col}) length ${wordPos.word.length}');
        if (row == wordPos.row &&
            col >= wordPos.col &&
            col < wordPos.col + wordPos.word.length) {
          debugPrint('_findWordFromPositions: found across word!');
          return {
            'number': wordPos.number ?? 1,
            'clue': _getClueForWord(wordPos.number ?? 1, direction),
            'word': wordPos.word,
            'startRow': wordPos.row,
            'startCol': wordPos.col,
          };
        }
      } else if (direction == Direction.down && wordPos.isDown) {
        debugPrint(
            '_findWordFromPositions: checking down word at (${wordPos.row}, ${wordPos.col}) length ${wordPos.word.length}');
        if (col == wordPos.col &&
            row >= wordPos.row &&
            row < wordPos.row + wordPos.word.length) {
          debugPrint('_findWordFromPositions: found down word!');
          return {
            'number': wordPos.number ?? 1,
            'clue': _getClueForWord(wordPos.number ?? 1, direction),
            'word': wordPos.word,
            'startRow': wordPos.row,
            'startCol': wordPos.col,
          };
        }
      }
    }
    return null;
  }

  // Method 2: Find word using wordIds in grid cells (fallback)
  Map<String, dynamic>? _findWordFromGridCells(
      int row, int col, Direction direction) {
    final cell = _crossword!.gridData[row][col];
    debugPrint(
        '_findWordFromGridCells: cell at ($row, $col) has wordIds: ${cell.wordIds}');

    if (cell.wordIds.isEmpty) {
      debugPrint('_findWordFromGridCells: no wordIds in cell');
      return null;
    }

    // For each wordId in the cell, try to reconstruct the word
    for (final wordId in cell.wordIds) {
      debugPrint('_findWordFromGridCells: checking wordId $wordId');

      final wordInfo = _reconstructWordFromGrid(row, col, wordId, direction);
      if (wordInfo != null) {
        debugPrint(
            '_findWordFromGridCells: successfully reconstructed word for wordId $wordId');
        return wordInfo;
      }
    }

    debugPrint('_findWordFromGridCells: could not reconstruct any word');
    return null;
  }

  // Reconstruct word information from grid cells (public method for external access)
  Map<String, dynamic>? reconstructWordFromGrid(
      int row, int col, int wordId, Direction direction) {
    return _reconstructWordFromGrid(row, col, wordId, direction);
  }

  // Reconstruct word information from grid cells
  Map<String, dynamic>? _reconstructWordFromGrid(
      int row, int col, int wordId, Direction direction) {
    try {
      List<String> wordChars = [];
      int startRow = row;
      int startCol = col;

      if (direction == Direction.across) {
        // Find start of word (move left until no wordId or edge)
        while (startCol > 0) {
          final prevCell = _crossword!.gridData[row][startCol - 1];
          if (!prevCell.wordIds.contains(wordId)) break;
          startCol--;
        }

        // Collect word characters (move right)
        int currentCol = startCol;
        while (currentCol < _crossword!.gridSize) {
          final currentCell = _crossword!.gridData[row][currentCol];
          if (!currentCell.wordIds.contains(wordId)) break;
          wordChars.add(currentCell.char);
          currentCol++;
        }
      } else {
        // Find start of word (move up until no wordId or edge)
        while (startRow > 0) {
          final prevCell = _crossword!.gridData[startRow - 1][col];
          if (!prevCell.wordIds.contains(wordId)) break;
          startRow--;
        }

        // Collect word characters (move down)
        int currentRow = startRow;
        while (currentRow < _crossword!.gridSize) {
          final currentCell = _crossword!.gridData[currentRow][col];
          if (!currentCell.wordIds.contains(wordId)) break;
          wordChars.add(currentCell.char);
          currentRow++;
        }
      }

      if (wordChars.length < 2) {
        debugPrint(
            '_reconstructWordFromGrid: word too short (${wordChars.length} chars)');
        return null;
      }

      final word = wordChars.join('');
      debugPrint(
          '_reconstructWordFromGrid: reconstructed word "$word" at ($startRow, $startCol)');

      // Try to find clue for this word
      String clue = _getClueForWordId(wordId, direction) ??
          _getClueForWord(wordId, direction);

      return {
        'number': wordId,
        'clue': clue,
        'word': word,
        'startRow': startRow,
        'startCol': startCol,
      };
    } catch (e) {
      debugPrint('_reconstructWordFromGrid: error reconstructing word: $e');
      return null;
    }
  }

  // Get clue for word ID and direction (for grid-based word finding)
  String? _getClueForWordId(int wordId, Direction direction) {
    if (_crossword == null) return null;

    final clueKey = wordId.toString();
    if (direction == Direction.across) {
      return _crossword!.clues.across[clueKey];
    } else {
      return _crossword!.clues.down[clueKey];
    }
  }

  // Get clue for word number and direction
  String _getClueForWord(int number, Direction direction) {
    if (_crossword == null) return '';

    final clueMap = direction == Direction.across
        ? _crossword!.clues.across
        : _crossword!.clues.down;

    return clueMap[number.toString()] ?? '';
  }

  // Update highlighted cells for current word
  void _updateHighlightedCells() {
    _highlightedCells.clear();

    if (_selectedCell == null || _crossword == null) return;

    debugPrint(
        '_updateHighlightedCells: updating for cell (${_selectedCell!.row}, ${_selectedCell!.col}) direction ${_selectedCell!.direction}');

    // Find all cells in the current word
    final wordInfo = _findWordAtPosition(
        _selectedCell!.row, _selectedCell!.col, _selectedCell!.direction);

    if (wordInfo == null) {
      debugPrint(
          '_updateHighlightedCells: no word info found, no highlighting');
      return;
    }

    debugPrint('_updateHighlightedCells: found word info: $wordInfo');

    // Method 1: Try using wordPositions (if available)
    bool highlightedFromPositions = _highlightFromWordPositions(wordInfo);

    if (!highlightedFromPositions || _highlightedCells.isEmpty) {
      // Method 2: Fallback - highlight using grid reconstruction
      debugPrint(
          '_updateHighlightedCells: falling back to grid-based highlighting (highlightedFromPositions=$highlightedFromPositions, highlightedCells=${_highlightedCells.length})');
      _highlightFromGridReconstruction(wordInfo);
    }

    debugPrint(
        '_updateHighlightedCells: highlighted ${_highlightedCells.length} cells');

    // Debug: Log all highlighted cells
    for (int i = 0; i < _highlightedCells.length; i++) {
      final cell = _highlightedCells[i];
      debugPrint(
          '_updateHighlightedCells: highlighted cell $i: (${cell.row}, ${cell.col})');
    }
  }

  // Method 1: Highlight using wordPositions array
  bool _highlightFromWordPositions(Map<String, dynamic> wordInfo) {
    debugPrint(
        '_highlightFromWordPositions: looking for word number ${wordInfo['number']} in direction ${_selectedCell!.direction}');

    for (final wordPos in _crossword!.wordPositions) {
      debugPrint(
          '_highlightFromWordPositions: checking wordPos number=${wordPos.number}, isAcross=${wordPos.isAcross}, isDown=${wordPos.isDown}, word="${wordPos.word}"');

      // Check if this is the correct word by number and direction
      bool isCorrectWord = false;
      if (wordPos.number == wordInfo['number']) {
        if (_selectedCell!.direction == Direction.across && wordPos.isAcross) {
          isCorrectWord = true;
        } else if (_selectedCell!.direction == Direction.down &&
            wordPos.isDown) {
          isCorrectWord = true;
        }
      }

      if (isCorrectWord) {
        debugPrint(
            '_highlightFromWordPositions: found matching word "${wordPos.word}" at (${wordPos.row}, ${wordPos.col})');

        // Check if wordPos.word is empty - if so, use the word from wordInfo
        String wordToHighlight =
            wordPos.word.isNotEmpty ? wordPos.word : (wordInfo['word'] ?? '');

        if (wordToHighlight.isEmpty) {
          debugPrint(
              '_highlightFromWordPositions: both wordPos.word and wordInfo.word are empty, cannot highlight');
          return false;
        }

        debugPrint(
            '_highlightFromWordPositions: using word "$wordToHighlight" for highlighting');

        if (_selectedCell!.direction == Direction.across) {
          for (int i = 0; i < wordToHighlight.length; i++) {
            _highlightedCells.add(SelectedCell(
              row: wordPos.row,
              col: wordPos.col + i,
              direction: Direction.across,
              wordNumber: wordPos.number ?? 1,
              clue: wordInfo['clue'] ?? '',
            ));
          }
          debugPrint(
              '_highlightFromWordPositions: highlighted ${wordToHighlight.length} cells for across word');
        } else {
          for (int i = 0; i < wordToHighlight.length; i++) {
            _highlightedCells.add(SelectedCell(
              row: wordPos.row + i,
              col: wordPos.col,
              direction: Direction.down,
              wordNumber: wordPos.number ?? 1,
              clue: wordInfo['clue'] ?? '',
            ));
          }
          debugPrint(
              '_highlightFromWordPositions: highlighted ${wordToHighlight.length} cells for down word');
        }
        return true;
      }
    }

    debugPrint('_highlightFromWordPositions: no matching word found');
    return false;
  }

  // Method 2: Highlight using grid reconstruction (fallback)
  void _highlightFromGridReconstruction(Map<String, dynamic> wordInfo) {
    if (!wordInfo.containsKey('startRow') ||
        !wordInfo.containsKey('startCol') ||
        !wordInfo.containsKey('word')) {
      debugPrint(
          '_highlightFromGridReconstruction: missing required fields in wordInfo');
      return;
    }

    final startRow = wordInfo['startRow'] as int;
    final startCol = wordInfo['startCol'] as int;
    final word = wordInfo['word'] as String;

    debugPrint(
        '_highlightFromGridReconstruction: highlighting word "$word" from ($startRow, $startCol) in direction ${_selectedCell!.direction}');

    for (int i = 0; i < word.length; i++) {
      final row = _selectedCell!.direction == Direction.across
          ? startRow
          : startRow + i;
      final col = _selectedCell!.direction == Direction.across
          ? startCol + i
          : startCol;

      // Validate coordinates are within grid bounds
      if (row >= 0 &&
          row < _crossword!.gridSize &&
          col >= 0 &&
          col < _crossword!.gridSize) {
        _highlightedCells.add(SelectedCell(
          row: row,
          col: col,
          direction: _selectedCell!.direction,
          wordNumber: wordInfo['number'] ?? 0,
          clue: wordInfo['clue'] ?? '',
        ));
        debugPrint(
            '_highlightFromGridReconstruction: added highlight at ($row, $col)');
      } else {
        debugPrint(
            '_highlightFromGridReconstruction: skipping out-of-bounds cell ($row, $col)');
      }
    }

    debugPrint(
        '_highlightFromGridReconstruction: highlighted ${_highlightedCells.length} cells total');
  }

  // Update completed cells count
  void _updateCompletedCells() {
    if (_crossword == null) return;

    int completed = 0;
    for (int row = 0; row < _crossword!.gridSize; row++) {
      for (int col = 0; col < _crossword!.gridSize; col++) {
        if (!_crossword!.gridData[row][col].isEmpty &&
            !_crossword!.gridData[row][col].isBlack &&
            _userInput[row][col].isNotEmpty) {
          completed++;
        }
      }
    }
    _completedCells = completed;
  }

  // Check if game is completed
  bool _isGameCompleted() {
    return _completedCells == _totalCells;
  }

  // Validate answer for a specific cell
  bool isCellCorrect(int row, int col) {
    if (_crossword == null ||
        row < 0 ||
        row >= _crossword!.gridSize ||
        col < 0 ||
        col >= _crossword!.gridSize) {
      return false;
    }

    final userChar = _userInput[row][col].toUpperCase();
    final correctChar = _crossword!.gridData[row][col].char.toUpperCase();

    return userChar.isNotEmpty && userChar == correctChar;
  }

  // Validate entire word
  bool isWordCorrect(int wordNumber, Direction direction) {
    if (_crossword == null) return false;

    debugPrint(
        'isWordCorrect: checking word $wordNumber in direction $direction');

    // Method 1: Try using wordPositions (preferred)
    bool? resultFromPositions =
        _validateWordFromPositions(wordNumber, direction);
    if (resultFromPositions != null) {
      debugPrint(
          'isWordCorrect: validated using wordPositions: $resultFromPositions');
      return resultFromPositions;
    }

    // Method 2: Fallback - validate using grid reconstruction
    debugPrint('isWordCorrect: falling back to grid-based validation');
    return _validateWordFromGridReconstruction(wordNumber, direction);
  }

  // Method 1: Validate word using wordPositions array
  bool? _validateWordFromPositions(int wordNumber, Direction direction) {
    for (final wordPos in _crossword!.wordPositions) {
      if (wordPos.number == wordNumber) {
        if ((direction == Direction.across && wordPos.isAcross) ||
            (direction == Direction.down && wordPos.isDown)) {
          debugPrint(
              '_validateWordFromPositions: validating word "${wordPos.word}"');

          // Check each character in the word
          for (int i = 0; i < wordPos.word.length; i++) {
            final row =
                direction == Direction.across ? wordPos.row : wordPos.row + i;
            final col =
                direction == Direction.across ? wordPos.col + i : wordPos.col;

            if (!isCellCorrect(row, col)) {
              debugPrint(
                  '_validateWordFromPositions: cell ($row, $col) is incorrect');
              return false;
            }
          }
          debugPrint('_validateWordFromPositions: word is correct');
          return true;
        }
      }
    }
    return null; // Word not found in wordPositions
  }

  // Method 2: Validate word using grid reconstruction (fallback)
  bool _validateWordFromGridReconstruction(
      int wordNumber, Direction direction) {
    // Find any cell that belongs to this word
    for (int row = 0; row < _crossword!.gridSize; row++) {
      for (int col = 0; col < _crossword!.gridSize; col++) {
        final cell = _crossword!.gridData[row][col];
        if (cell.wordIds.contains(wordNumber)) {
          // Found a cell belonging to this word, reconstruct it
          final wordInfo =
              _reconstructWordFromGrid(row, col, wordNumber, direction);
          if (wordInfo != null) {
            final startRow = wordInfo['startRow'] as int;
            final startCol = wordInfo['startCol'] as int;
            final word = wordInfo['word'] as String;

            debugPrint(
                '_validateWordFromGridReconstruction: validating reconstructed word "$word"');

            // Check each character in the reconstructed word
            for (int i = 0; i < word.length; i++) {
              final cellRow =
                  direction == Direction.across ? startRow : startRow + i;
              final cellCol =
                  direction == Direction.across ? startCol + i : startCol;

              if (!isCellCorrect(cellRow, cellCol)) {
                debugPrint(
                    '_validateWordFromGridReconstruction: cell ($cellRow, $cellCol) is incorrect');
                return false;
              }
            }
            debugPrint('_validateWordFromGridReconstruction: word is correct');
            return true;
          }
        }
      }
    }

    debugPrint('_validateWordFromGridReconstruction: word not found');
    return false;
  }

  // Get all incorrect cells
  List<SelectedCell> getIncorrectCells() {
    final incorrectCells = <SelectedCell>[];

    if (_crossword == null) return incorrectCells;

    for (int row = 0; row < _crossword!.gridSize; row++) {
      for (int col = 0; col < _crossword!.gridSize; col++) {
        final cell = _crossword!.gridData[row][col];
        if (!cell.isEmpty && !cell.isBlack) {
          final userChar = _userInput[row][col].toUpperCase();
          if (userChar.isNotEmpty && userChar != cell.char.toUpperCase()) {
            incorrectCells.add(SelectedCell(
              row: row,
              col: col,
              direction: Direction.across, // Default direction
              wordNumber: 0,
              clue: '',
            ));
          }
        }
      }
    }

    return incorrectCells;
  }

  // Complete the game
  void _completeGame() {
    _gameState = GameState.completed;
    _updateTimeSpent();

    // Track completion in statistics
    if (_crossword != null && _userStatsProvider != null) {
      _userStatsProvider!.recordGameCompletion(
        _crossword!.id,
        _timeSpent,
        _completedCells,
        _hintsUsed,
        _crossword!.categoryName,
      );
    }

    // Provide completion haptic feedback
    if (_vibrationEnabled) {
      HapticFeedback.heavyImpact();
    }

    // Save progress
    _saveProgress();
  }

  // Update time spent
  void _updateTimeSpent() {
    if (_gameStartTime != null) {
      _timeSpent += DateTime.now().difference(_gameStartTime!);
      _gameStartTime = null;
    }
  }

  // Load game settings
  Future<void> _loadSettings() async {
    _autoMoveEnabled = await _storageService.getAutoMoveEnabled();
    _vibrationEnabled = await _storageService.getVibrationEnabled();
    _showErrors =
        await _storageService.getBoolSetting('show_errors', defaultValue: true);
  }

  // Save game progress
  Future<void> _saveProgress() async {
    if (_crossword == null) return;

    final progress = Progress(
      id: '${_crossword!.id}_progress',
      crosswordId: _crossword!.id,
      userId: 'current_user', // This should come from auth provider
      userInput: _userInput,
      completedCells: _completedCells,
      totalCells: _totalCells,
      timeSpent: _timeSpent,
      isCompleted: _gameState == GameState.completed,
      lastPlayed: DateTime.now(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await _storageService.setCrosswordProgress(_crossword!.id, progress);
  }

  // Load game progress
  Future<void> _loadProgress() async {
    if (_crossword == null) return;

    final progress = await _storageService.getCrosswordProgress(_crossword!.id);
    if (progress != null) {
      _userInput = progress.userInput;
      _timeSpent = progress.timeSpent;
      _completedCells = progress.completedCells;
      _totalCells = progress.totalCells;

      if (progress.isCompleted) {
        _gameState = GameState.completed;
      }
    }
  }

  // Keyboard navigation methods
  void moveUp() {
    if (_selectedCell == null || _crossword == null) return;

    final currentRow = _selectedCell!.row;
    final currentCol = _selectedCell!.col;

    // Find the next valid cell above
    for (int row = currentRow - 1; row >= 0; row--) {
      if (_isValidCell(row, currentCol)) {
        selectCell(row, currentCol);
        break;
      }
    }
  }

  void moveDown() {
    if (_selectedCell == null || _crossword == null) return;

    final currentRow = _selectedCell!.row;
    final currentCol = _selectedCell!.col;

    // Find the next valid cell below
    for (int row = currentRow + 1; row < _crossword!.gridSize; row++) {
      if (_isValidCell(row, currentCol)) {
        selectCell(row, currentCol);
        break;
      }
    }
  }

  void moveLeft() {
    if (_selectedCell == null || _crossword == null) return;

    final currentRow = _selectedCell!.row;
    final currentCol = _selectedCell!.col;

    // Find the next valid cell to the left
    for (int col = currentCol - 1; col >= 0; col--) {
      if (_isValidCell(currentRow, col)) {
        selectCell(currentRow, col);
        break;
      }
    }
  }

  void moveRight() {
    if (_selectedCell == null || _crossword == null) return;

    final currentRow = _selectedCell!.row;
    final currentCol = _selectedCell!.col;

    // Find the next valid cell to the right
    for (int col = currentCol + 1; col < _crossword!.gridSize; col++) {
      if (_isValidCell(currentRow, col)) {
        selectCell(currentRow, col);
        break;
      }
    }
  }

  // Toggle direction for current cell
  void toggleDirection() {
    if (_selectedCell == null) return;

    final currentRow = _selectedCell!.row;
    final currentCol = _selectedCell!.col;
    final currentDirection = _selectedCell!.direction;
    final newDirection = currentDirection == Direction.across
        ? Direction.down
        : Direction.across;

    debugPrint(
        'toggleDirection: attempting to toggle from $currentDirection to $newDirection');

    // Try to find word in the new direction
    final newWordInfo =
        _findWordAtPosition(currentRow, currentCol, newDirection);

    if (newWordInfo != null) {
      debugPrint('toggleDirection: found word in new direction, switching');
      _selectedCell = SelectedCell(
        row: currentRow,
        col: currentCol,
        direction: newDirection,
        wordNumber: newWordInfo['number'],
        clue: newWordInfo['clue'],
      );
      _currentWordInfo = newWordInfo;

      // Update highlighted cells for new direction
      _updateHighlightedCells();

      // Provide haptic feedback
      if (_vibrationEnabled) {
        HapticFeedback.selectionClick();
      }

      debugPrint('toggleDirection: direction toggled successfully');
      notifyListeners();
    } else {
      debugPrint('toggleDirection: no word found in new direction');
      // Fallback to the old method if new method fails
      selectCell(currentRow, currentCol, preferredDirection: newDirection);
    }
  }

  // Reveal current cell (hint functionality)
  void revealCurrentCell() {
    if (_selectedCell == null || _crossword == null) return;

    final row = _selectedCell!.row;
    final col = _selectedCell!.col;
    final correctChar = _crossword!.gridData[row][col].char;

    _userInput[row][col] = correctChar.toUpperCase();
    _updateCompletedCells();
    _hintsUsed++;

    // Move to next cell if auto-move is enabled
    if (_autoMoveEnabled) {
      _moveToNextCell();
    }

    notifyListeners();
  }

  // Reveal entire current word
  void revealCurrentWord() {
    if (_selectedCell == null || _crossword == null) return;

    debugPrint(
        'revealCurrentWord: revealing word for cell (${_selectedCell!.row}, ${_selectedCell!.col})');

    final wordInfo = _findWordAtPosition(
        _selectedCell!.row, _selectedCell!.col, _selectedCell!.direction);

    if (wordInfo == null) {
      debugPrint('revealCurrentWord: no word info found');
      return;
    }

    debugPrint('revealCurrentWord: found word info: $wordInfo');

    // Method 1: Try using wordPositions (preferred)
    bool revealedFromPositions = _revealWordFromPositions(wordInfo);

    if (!revealedFromPositions) {
      // Method 2: Fallback - reveal using grid reconstruction
      debugPrint('revealCurrentWord: falling back to grid-based reveal');
      _revealWordFromGridReconstruction(wordInfo);
    }

    _updateCompletedCells();
    _hintsUsed++;
    notifyListeners();
  }

  // Method 1: Reveal word using wordPositions array
  bool _revealWordFromPositions(Map<String, dynamic> wordInfo) {
    for (final wordPos in _crossword!.wordPositions) {
      if (wordPos.number == wordInfo['number']) {
        if ((_selectedCell!.direction == Direction.across &&
                wordPos.isAcross) ||
            (_selectedCell!.direction == Direction.down && wordPos.isDown)) {
          debugPrint(
              '_revealWordFromPositions: revealing word "${wordPos.word}"');

          for (int i = 0; i < wordPos.word.length; i++) {
            final row = _selectedCell!.direction == Direction.across
                ? wordPos.row
                : wordPos.row + i;
            final col = _selectedCell!.direction == Direction.across
                ? wordPos.col + i
                : wordPos.col;

            _userInput[row][col] = wordPos.word[i].toUpperCase();
          }
          return true;
        }
      }
    }
    return false;
  }

  // Method 2: Reveal word using grid reconstruction (fallback)
  void _revealWordFromGridReconstruction(Map<String, dynamic> wordInfo) {
    if (!wordInfo.containsKey('startRow') ||
        !wordInfo.containsKey('startCol') ||
        !wordInfo.containsKey('word')) {
      debugPrint(
          '_revealWordFromGridReconstruction: missing required fields in wordInfo');
      return;
    }

    final startRow = wordInfo['startRow'] as int;
    final startCol = wordInfo['startCol'] as int;
    final word = wordInfo['word'] as String;

    debugPrint(
        '_revealWordFromGridReconstruction: revealing word "$word" from ($startRow, $startCol)');

    for (int i = 0; i < word.length; i++) {
      final row = _selectedCell!.direction == Direction.across
          ? startRow
          : startRow + i;
      final col = _selectedCell!.direction == Direction.across
          ? startCol + i
          : startCol;

      // Get the correct character from grid data
      final correctChar = _crossword!.gridData[row][col].char;
      _userInput[row][col] = correctChar.toUpperCase();
    }

    debugPrint(
        '_revealWordFromGridReconstruction: revealed ${word.length} characters');
  }

  // Clear current cell
  void clearCurrentCell() {
    if (_selectedCell == null) return;

    _userInput[_selectedCell!.row][_selectedCell!.col] = '';
    _updateCompletedCells();
    notifyListeners();
  }

  // Clear entire current word
  void clearCurrentWord() {
    if (_selectedCell == null || _crossword == null) return;

    debugPrint(
        'clearCurrentWord: clearing word for cell (${_selectedCell!.row}, ${_selectedCell!.col})');

    final wordInfo = _findWordAtPosition(
        _selectedCell!.row, _selectedCell!.col, _selectedCell!.direction);

    if (wordInfo == null) {
      debugPrint('clearCurrentWord: no word info found');
      return;
    }

    debugPrint('clearCurrentWord: found word info: $wordInfo');

    // Method 1: Try using wordPositions (preferred)
    bool clearedFromPositions = _clearWordFromPositions(wordInfo);

    if (!clearedFromPositions) {
      // Method 2: Fallback - clear using grid reconstruction
      debugPrint('clearCurrentWord: falling back to grid-based clear');
      _clearWordFromGridReconstruction(wordInfo);
    }

    _updateCompletedCells();
    notifyListeners();
  }

  // Method 1: Clear word using wordPositions array
  bool _clearWordFromPositions(Map<String, dynamic> wordInfo) {
    for (final wordPos in _crossword!.wordPositions) {
      if (wordPos.number == wordInfo['number']) {
        if ((_selectedCell!.direction == Direction.across &&
                wordPos.isAcross) ||
            (_selectedCell!.direction == Direction.down && wordPos.isDown)) {
          debugPrint(
              '_clearWordFromPositions: clearing word "${wordPos.word}"');

          for (int i = 0; i < wordPos.word.length; i++) {
            final row = _selectedCell!.direction == Direction.across
                ? wordPos.row
                : wordPos.row + i;
            final col = _selectedCell!.direction == Direction.across
                ? wordPos.col + i
                : wordPos.col;

            _userInput[row][col] = '';
          }
          return true;
        }
      }
    }
    return false;
  }

  // Method 2: Clear word using grid reconstruction (fallback)
  void _clearWordFromGridReconstruction(Map<String, dynamic> wordInfo) {
    if (!wordInfo.containsKey('startRow') ||
        !wordInfo.containsKey('startCol') ||
        !wordInfo.containsKey('word')) {
      debugPrint(
          '_clearWordFromGridReconstruction: missing required fields in wordInfo');
      return;
    }

    final startRow = wordInfo['startRow'] as int;
    final startCol = wordInfo['startCol'] as int;
    final word = wordInfo['word'] as String;

    debugPrint(
        '_clearWordFromGridReconstruction: clearing word "$word" from ($startRow, $startCol)');

    for (int i = 0; i < word.length; i++) {
      final row = _selectedCell!.direction == Direction.across
          ? startRow
          : startRow + i;
      final col = _selectedCell!.direction == Direction.across
          ? startCol + i
          : startCol;

      _userInput[row][col] = '';
    }

    debugPrint(
        '_clearWordFromGridReconstruction: cleared ${word.length} characters');
  }

  // Settings methods
  void setAutoMove(bool enabled) {
    _autoMoveEnabled = enabled;
    _storageService.setAutoMoveEnabled(enabled);
    notifyListeners();
  }

  void setVibration(bool enabled) {
    _vibrationEnabled = enabled;
    _storageService.setVibrationEnabled(enabled);
    notifyListeners();
  }

  void setShowErrors(bool enabled) {
    _showErrors = enabled;
    _storageService.setBoolSetting('show_errors', enabled);
    notifyListeners();
  }

  // Force refresh highlighting (for debugging)
  void refreshHighlighting() {
    debugPrint('refreshHighlighting: forcing highlight refresh');
    if (_selectedCell != null) {
      _updateHighlightedCells();
      notifyListeners();
    }
  }

  // Clear game state
  void clearGame() {
    _crossword = null;
    _userInput.clear();
    _gameState = GameState.idle;
    _selectedCell = null;
    _highlightedCells.clear();
    _timeSpent = Duration.zero;
    _gameStartTime = null;
    _completedCells = 0;
    _totalCells = 0;
    notifyListeners();
  }
}
