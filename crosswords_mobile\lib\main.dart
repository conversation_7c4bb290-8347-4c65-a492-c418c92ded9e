import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:google_fonts/google_fonts.dart';

import 'core/config/app_config.dart';
import 'core/theme/app_theme.dart';
import 'core/router/app_router.dart';
import 'core/services/api_service.dart';
import 'core/services/auth_service.dart';
import 'core/services/storage_service.dart';
import 'core/services/connectivity_service.dart';
import 'core/services/sync_service.dart';
import 'providers/auth_provider.dart';
import 'providers/crossword_provider.dart';
import 'providers/crossword_game_provider.dart';
import 'providers/theme_provider.dart';
import 'providers/offline_provider.dart';
import 'providers/user_stats_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive for local storage
  await Hive.initFlutter();

  // Initialize services
  await StorageService.init();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);

  runApp(const CrosswordsApp());
}

class CrosswordsApp extends StatelessWidget {
  const CrosswordsApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Services
        Provider<ApiService>(
          create: (_) => ApiService(AppConfig.baseUrl),
        ),
        Provider<AuthService>(
          create: (context) => AuthService(
            context.read<ApiService>(),
            StorageService(),
          ),
        ),

        // State Providers
        ChangeNotifierProvider<ThemeProvider>(
          create: (_) => ThemeProvider(),
        ),
        ChangeNotifierProvider<AuthProvider>(
          create: (context) => AuthProvider(
            context.read<AuthService>(),
          ),
        ),
        ChangeNotifierProvider<CrosswordProvider>(
          create: (context) => CrosswordProvider(
            context.read<ApiService>(),
          ),
        ),
        ChangeNotifierProvider<CrosswordGameProvider>(
          create: (_) => CrosswordGameProvider(),
        ),

        // Services as ChangeNotifiers
        ChangeNotifierProvider<ConnectivityService>(
          create: (_) => ConnectivityService()..initialize(),
        ),
        ChangeNotifierProvider<SyncService>(
          create: (_) => SyncService()..initialize(),
        ),

        // Additional Providers
        ChangeNotifierProvider<OfflineProvider>(
          create: (context) => OfflineProvider(
            context.read<ApiService>(),
          )..initialize(),
        ),
        ChangeNotifierProvider<UserStatsProvider>(
          create: (_) => UserStatsProvider()..initialize(),
        ),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp.router(
            title: 'tekateki.io',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeProvider.themeMode,
            routerConfig: AppRouter.router,
            builder: (context, child) {
              return MediaQuery(
                data: MediaQuery.of(context).copyWith(
                  textScaler:
                      const TextScaler.linear(1.0), // Prevent text scaling
                ),
                child: child!,
              );
            },
          );
        },
      ),
    );
  }
}
