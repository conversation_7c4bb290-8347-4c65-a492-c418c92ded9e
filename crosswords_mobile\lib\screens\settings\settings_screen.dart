import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/theme_provider.dart';
import '../../providers/offline_provider.dart';
import '../../providers/user_stats_provider.dart';
import '../../core/services/storage_service.dart';
import '../../widgets/common/connectivity_status_widget.dart';
import '../offline/offline_management_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final StorageService _storageService = StorageService();

  bool _autoMoveEnabled = true;
  bool _vibrationEnabled = true;
  bool _showErrors = true;
  bool _autoSyncEnabled = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final autoMove = await _storageService.getAutoMoveEnabled();
    final vibration = await _storageService.getVibrationEnabled();
    final showErrors =
        await _storageService.getBoolSetting('show_errors', defaultValue: true);
    final autoSync =
        await _storageService.getBoolSetting('auto_sync', defaultValue: true);

    setState(() {
      _autoMoveEnabled = autoMove;
      _vibrationEnabled = vibration;
      _showErrors = showErrors;
      _autoSyncEnabled = autoSync;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Pengaturan'),
        elevation: 0,
      ),
      body: Column(
        children: [
          // Connectivity Status
          const ConnectivityStatusWidget(compact: true),

          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // Game Settings
                _buildSectionHeader('Pengaturan Permainan', theme),
                _buildGameSettings(theme),

                const SizedBox(height: 24),

                // Offline Settings
                _buildSectionHeader('Pengaturan Offline', theme),
                _buildOfflineSettings(theme),

                const SizedBox(height: 24),

                // Appearance Settings
                _buildSectionHeader('Tampilan', theme),
                _buildAppearanceSettings(theme),

                const SizedBox(height: 24),

                // Data Management
                _buildSectionHeader('Manajemen Data', theme),
                _buildDataManagement(theme),

                const SizedBox(height: 24),

                // About
                _buildSectionHeader('Tentang', theme),
                _buildAboutSection(theme),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildGameSettings(ThemeData theme) {
    return Card(
      child: Column(
        children: [
          SwitchListTile(
            title: const Text('Auto Move'),
            subtitle:
                const Text('Pindah otomatis ke sel berikutnya setelah mengisi'),
            value: _autoMoveEnabled,
            onChanged: (value) async {
              setState(() {
                _autoMoveEnabled = value;
              });
              await _storageService.setAutoMoveEnabled(value);
            },
          ),
          const Divider(height: 1),
          SwitchListTile(
            title: const Text('Getaran'),
            subtitle: const Text('Berikan feedback getaran saat bermain'),
            value: _vibrationEnabled,
            onChanged: (value) async {
              setState(() {
                _vibrationEnabled = value;
              });
              await _storageService.setVibrationEnabled(value);
            },
          ),
          const Divider(height: 1),
          SwitchListTile(
            title: const Text('Tampilkan Kesalahan'),
            subtitle: const Text('Highlight sel yang salah secara otomatis'),
            value: _showErrors,
            onChanged: (value) async {
              setState(() {
                _showErrors = value;
              });
              await _storageService.setBoolSetting('show_errors', value);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildOfflineSettings(ThemeData theme) {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.cloud_download),
            title: const Text('Manajemen Offline'),
            subtitle: Consumer<OfflineProvider>(
              builder: (context, offlineProvider, child) {
                return Text(
                    '${offlineProvider.downloadedCrosswordIds.length} teka-teki tersimpan');
              },
            ),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const OfflineManagementScreen(),
                ),
              );
            },
          ),
          const Divider(height: 1),
          SwitchListTile(
            title: const Text('Sinkronisasi Otomatis'),
            subtitle: const Text('Sinkronkan data secara otomatis saat online'),
            value: _autoSyncEnabled,
            onChanged: (value) async {
              setState(() {
                _autoSyncEnabled = value;
              });
              await _storageService.setBoolSetting('auto_sync', value);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAppearanceSettings(ThemeData theme) {
    return Card(
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return ListTile(
            leading: const Icon(Icons.palette),
            title: const Text('Tema'),
            subtitle: Text(_getThemeLabel(themeProvider.themeMode)),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showThemeDialog(themeProvider),
          );
        },
      ),
    );
  }

  Widget _buildDataManagement(ThemeData theme) {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.analytics),
            title: const Text('Statistik Pengguna'),
            subtitle: Consumer<UserStatsProvider>(
              builder: (context, statsProvider, child) {
                return Text(
                    '${statsProvider.totalGamesPlayed} permainan dimainkan');
              },
            ),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              // Navigate to detailed stats
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.delete_sweep, color: Colors.red),
            title: const Text('Reset Statistik'),
            subtitle: const Text('Hapus semua data statistik permainan'),
            onTap: () => _showResetStatsDialog(),
          ),
        ],
      ),
    );
  }

  Widget _buildAboutSection(ThemeData theme) {
    return Card(
      child: Column(
        children: [
          const ListTile(
            leading: Icon(Icons.info),
            title: Text('Versi Aplikasi'),
            subtitle: Text('1.0.0'),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.privacy_tip),
            title: const Text('Kebijakan Privasi'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              // Open privacy policy
            },
          ),
        ],
      ),
    );
  }

  String _getThemeLabel(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'Terang';
      case ThemeMode.dark:
        return 'Gelap';
      case ThemeMode.system:
        return 'Mengikuti Sistem';
    }
  }

  void _showThemeDialog(ThemeProvider themeProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Pilih Tema'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<ThemeMode>(
              title: const Text('Terang'),
              value: ThemeMode.light,
              groupValue: themeProvider.themeMode,
              onChanged: (value) {
                if (value != null) {
                  themeProvider.setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('Gelap'),
              value: ThemeMode.dark,
              groupValue: themeProvider.themeMode,
              onChanged: (value) {
                if (value != null) {
                  themeProvider.setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('Mengikuti Sistem'),
              value: ThemeMode.system,
              groupValue: themeProvider.themeMode,
              onChanged: (value) {
                if (value != null) {
                  themeProvider.setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showResetStatsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Statistik'),
        content: const Text(
          'Yakin ingin menghapus semua statistik permainan? '
          'Tindakan ini tidak dapat dibatalkan.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Batal'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await context.read<UserStatsProvider>().resetStatistics();

              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Statistik berhasil direset'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}
